## Table of Contents

- [Workflow Examples and Complete Walkthroughs](#workflow-examples-and-complete-walkthroughs)
  - [Complete Feature Development Walkthrough](#complete-feature-development-walkthrough)
    - [The Story of a Task Management Feature](#the-story-of-a-task-management-feature)
      - [Phase 1: Planning and Discovery](#phase-1-planning-and-discovery)
  - [Epic](#epic)
  - [Capabilities](#capabilities)
- [Search for existing authentication functions](#search-for-existing-authentication-functions)
- [Returns: validateUser, checkPermissions, auditAction](#returns-validateuser-checkpermissions-auditaction)
- [Search for task-related modules](#search-for-task-related-modules)
- [Returns: module:task-services, module:task-validators](#returns-moduletask-services-moduletask-validators)
- [Explore task services module](#explore-task-services-module)
      - [Phase 2: Architecture and Design](#phase-2-architecture-and-design)
      - [Phase 3: Implementation and Validation](#phase-3-implementation-and-validation)
      - [Phase 4: Integration and Deployment](#phase-4-integration-and-deployment)
- [Health check validation](#health-check-validation)
- [Response: {"status": "healthy", "version": "1.0.0"}](#response-status-healthy-version-100)
- [Metrics endpoint validation](#metrics-endpoint-validation)
- [Response: Prometheus metrics format](#response-prometheus-metrics-format)
- [Integration test in production](#integration-test-in-production)
- [All critical paths verified](#all-critical-paths-verified)
    - [The Outcome: Discoverable Architecture](#the-outcome-discoverable-architecture)
  - [Database-Driven Implementation Example](#database-driven-implementation-example)
    - [Advanced Feature: User Profile Management](#advanced-feature-user-profile-management)
      - [Human-Led Planning Phase](#human-led-planning-phase)
- [Discover user-related modules](#discover-user-related-modules)
- [Returns: module:user-validators, module:user-services, module:profile-handlers](#returns-moduleuser-validators-moduleuser-services-moduleprofile-handlers)
- [Explore user services in detail](#explore-user-services-in-detail)
      - [AI-Driven Implementation Phase](#ai-driven-implementation-phase)
  - [Component Integration Scenarios](#component-integration-scenarios)
    - [Scenario 1: Library Integration](#scenario-1-library-integration)
- [Current user-registration service](#current-user-registration-service)
- [Updated user-registration service](#updated-user-registration-service)
    - [Scenario 2: API Evolution](#scenario-2-api-evolution)
    - [Scenario 3: Resource Migration](#scenario-3-resource-migration)
- [Original database resource](#original-database-resource)
- [New database resource](#new-database-resource)
  - [End-to-End Validation Examples](#end-to-end-validation-examples)
    - [Validation Level 1: Component Integrity](#validation-level-1-component-integrity)
- [Validate all YAML files](#validate-all-yaml-files)
- [Check catalog-info.yaml completeness](#check-catalog-infoyaml-completeness)
- [Verify soul.yaml structure](#verify-soulyaml-structure)
- [Check cross-references](#check-cross-references)
    - [Validation Level 2: System Coherence](#validation-level-2-system-coherence)
    - [Validation Level 3: Operational Readiness](#validation-level-3-operational-readiness)
  - [Best Practices and Anti-Patterns](#best-practices-and-anti-patterns)
    - [Best Practices](#best-practices)
    - [Anti-Patterns to Avoid](#anti-patterns-to-avoid)
  - [Additional Practical Examples](#additional-practical-examples)
    - [Image Processing Feature Walkthrough](#image-processing-feature-walkthrough)
      - [The Feature Story](#the-feature-story)
      - [Visual Architecture](#visual-architecture)
      - [Draft catalog-info.yaml Evolution](#draft-catalog-infoyaml-evolution)
      - [Validation Hook Script Example](#validation-hook-script-example)
- [.cortex/hooks/validate-image-service.sh](#cortexhooksvalidate-image-servicesh)
- [Check API endpoints](#check-api-endpoints)
- [Validate image processing capabilities](#validate-image-processing-capabilities)
- [Check storage connectivity](#check-storage-connectivity)
- [Verify metadata extraction](#verify-metadata-extraction)
      - [The Payoff: Discoverable Architecture](#the-payoff-discoverable-architecture)
    - [Database-Driven Feature Creation Examples](#database-driven-feature-creation-examples)
      - [Advanced User Management System](#advanced-user-management-system)
      - [Human-Led Planning Phase](#human-led-planning-phase)
- [Create the feature in the component database](#create-the-feature-in-the-component-database)
- [Discover all authentication-related modules](#discover-all-authentication-related-modules)
- [Returns: module:auth-core, module:rbac-engine, module:audit-logger](#returns-moduleauth-core-modulerbac-engine-moduleaudit-logger)
- [Explore RBAC engine capabilities](#explore-rbac-engine-capabilities)
- [Update feature with carefully selected dependencies](#update-feature-with-carefully-selected-dependencies)
      - [AI Implementation with Rich Context](#ai-implementation-with-rich-context)
      - [Comprehensive Validation Pipeline](#comprehensive-validation-pipeline)
    - [Complex Component Integration Scenarios](#complex-component-integration-scenarios)
      - [Scenario: Microservices Orchestration](#scenario-microservices-orchestration)
- [Order orchestration system](#order-orchestration-system)
      - [Scenario: Event-Driven Architecture](#scenario-event-driven-architecture)

---

# Workflow Examples and Complete Walkthroughs

This document provides comprehensive walkthrough examples of the Cortex methodology in action, demonstrating end-to-end implementation scenarios across different component types and complexity levels.

## Complete Feature Development Walkthrough

### The Story of a Task Management Feature

This comprehensive example follows the complete journey of building a task management feature from initial concept through production deployment, demonstrating all four phases of the Cortex methodology.

#### Phase 1: Planning and Discovery

**Initial Feature Concept**
```markdown
Feature: Task Creation and Management
Purpose: Allow users to create, edit, and manage tasks with validation and notifications
Owner: Product Team
Estimated Complexity: Medium
```

**Epic Decomposition**
Using the Block Assembly template, we break down the feature:

```markdown
## Epic
- ID: feature:task-management
- Title: Task Management - Creation and lifecycle management
- Owner: Product Team
- Status: Planning

## Capabilities
- Create new tasks with validation
- Edit existing task properties
- Delete tasks with confirmation
- Send notifications on task state changes
- Audit task operations for compliance
```

**Dependency Discovery Process**
```bash
# Search for existing authentication functions
GET /api/components?type=function&category=auth
# Returns: validateUser, checkPermissions, auditAction

# Search for task-related modules
GET /api/components?type=module&name=*task*
# Returns: module:task-services, module:task-validators

# Explore task services module
GET /api/modules/module:task-services/details
```

**Block Assessment Results**
| Block ID | Layer | Signature | Summary | Score | Decision |
|----------|-------|-----------|---------|-------|----------|
| function:validateTaskInput | domain | (task: TaskInput) => ValidationResult | Validates task data structure | 22 | Reuse |
| function:saveTaskToDatabase | foundation | (task: Task) => Promise<TaskId> | Persists task to database | 20 | Reuse |
| function:sendTaskNotification | foundation | (event: TaskEvent) => Promise<void> | Sends notifications | 19 | Reuse |
| function:auditTaskOperation | domain | (operation: TaskOperation) => void | Logs operations | 16 | Refactor |

#### Phase 2: Architecture and Design

**Component Architecture Design**

```mermaid
graph TD
    A[Web Portal] --> B[Task API]
    B --> C[Task Service]
    C --> D[Task Validators]
    C --> E[Database]
    C --> F[Notification Service]
    C --> G[Audit Service]
    
    H[Auth Service] --> B
    I[User Management] --> C
```

**Draft catalog-info.yaml**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: task-management-api
  description: RESTful API for task creation and management
  annotations:
    backstage.io/techdocs-ref: dir:.
    cortexatlas.io/soul-file: './soul.yaml'
spec:
  type: service
  lifecycle: experimental
  owner: product-team
  system: task-management
  providesApis:
    - task-api
  consumesApis:
    - auth-api
    - notification-api
  dependsOn:
    - postgres-tasks-db
```

**Initial soul.yaml Structure**
```yaml
entityMetadata:
  uuid: "550e8400-e29b-41d4-a716-************"
  created: "2024-01-15"
  version: "0.1.0"

operationalProfile:
  availability: "99.5% target"
  deploymentPlatform: "Kubernetes"
  monitoringEndpoints:
    health: "/health"
    metrics: "/metrics"

dependencies:
  critical:
    - type: resource
      name: postgres-tasks-db
    - type: service
      name: auth-service
  optional:
    - type: service
      name: notification-service

functions:
  createTask:
    signature: "(taskData: TaskInput) => Promise<Task>"
    description: "Creates a new task with validation"
    uses:
      - function:validateTaskInput
      - function:saveTaskToDatabase
      - function:sendTaskNotification
```

#### Phase 3: Implementation and Validation

**AI Implementation Context**
```javascript
const implementationContext = {
  feature: {
    id: "feature:task-management",
    title: "Task Management API",
    approvedDependencies: [
      "function:validateTaskInput",
      "function:saveTaskToDatabase", 
      "function:sendTaskNotification",
      "function:auditTaskOperation"
    ]
  },
  constraints: [
    "MUST only import approved functions",
    "MUST follow existing error handling patterns",
    "MUST include comprehensive TypeScript types",
    "MUST maintain backward compatibility"
  ],
  moduleContext: {
    "module:task-services": {
      patterns: "Repository pattern with dependency injection",
      errorHandling: "Result<T, Error> pattern",
      testing: "Jest with supertest for integration"
    }
  }
};
```

**Implementation Code Example**
```typescript
// src/services/TaskService.ts
import { validateTaskInput } from '@/validators/task-validators';
import { saveTaskToDatabase } from '@/repositories/task-repository';
import { sendTaskNotification } from '@/services/notification-service';
import { auditTaskOperation } from '@/services/audit-service';

export class TaskService {
  async createTask(taskData: TaskInput): Promise<Result<Task, ValidationError>> {
    // Validate input using approved function
    const validationResult = validateTaskInput(taskData);
    if (!validationResult.isValid) {
      return Result.error(new ValidationError(validationResult.errors));
    }

    try {
      // Save to database using approved function
      const taskId = await saveTaskToDatabase(validationResult.data);
      
      // Send notification using approved function
      await sendTaskNotification({
        type: 'task.created',
        taskId,
        userId: taskData.createdBy
      });

      // Audit the operation using approved function
      auditTaskOperation({
        operation: 'create',
        taskId,
        userId: taskData.createdBy,
        timestamp: new Date()
      });

      return Result.success(taskId);
    } catch (error) {
      return Result.error(error);
    }
  }
}
```

**Validation Pipeline Results**
```bash
✅ Static dependency analysis passed
✅ Function existence verification passed  
✅ TypeScript compilation successful
✅ Unit tests: 15/15 passing
✅ Integration tests: 8/8 passing
✅ Code coverage: 94% (above 90% threshold)
✅ Security scan: No vulnerabilities found
```

#### Phase 4: Integration and Deployment

**Final catalog-info.yaml**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: task-management-api
  description: Production-ready RESTful API for task creation and management
  annotations:
    backstage.io/techdocs-ref: dir:.
    cortexatlas.io/soul-file: './soul.yaml'
    prometheus.io/scrape: 'true'
    prometheus.io/port: '3000'
    prometheus.io/path: '/metrics'
spec:
  type: service
  lifecycle: production
  owner: product-team
  system: task-management
  providesApis:
    - task-api
  consumesApis:
    - auth-api
    - notification-api
  dependsOn:
    - postgres-tasks-db
    - redis-cache
```

**Production soul.yaml**
```yaml
entityMetadata:
  uuid: "550e8400-e29b-41d4-a716-************"
  created: "2024-01-15"
  version: "1.0.0"
  lastUpdated: "2024-02-01"

operationalProfile:
  availability: "99.9% SLA"
  deploymentPlatform: "Kubernetes on AWS EKS"
  scaling:
    min: 2
    max: 10
    targetCPU: 70
  monitoringEndpoints:
    health: "/health"
    metrics: "/metrics"
    readiness: "/ready"

performanceProfile:
  expectedLatency: "< 150ms p95"
  throughput: "500 requests/second"
  resourceLimits:
    cpu: "500m"
    memory: "512Mi"

securityProfile:
  dataClassification: "Internal"
  authentication: "JWT Bearer tokens"
  authorization: "RBAC with task ownership"
  encryption: "TLS 1.3 in transit, AES-256 at rest"

eventContracts:
  publishes:
    - event: "task.created"
      schema: "TaskCreatedEvent"
    - event: "task.updated"
      schema: "TaskUpdatedEvent"
    - event: "task.deleted"
      schema: "TaskDeletedEvent"
```

**Deployment Verification**
```bash
# Health check validation
curl https://api.company.com/tasks/health
# Response: {"status": "healthy", "version": "1.0.0"}

# Metrics endpoint validation
curl https://api.company.com/tasks/metrics
# Response: Prometheus metrics format

# Integration test in production
npm run test:production-smoke
# All critical paths verified
```

### The Outcome: Discoverable Architecture

**Backstage Catalog View**
The completed feature appears in Backstage with:
- Clear ownership and lifecycle information
- Visual dependency graph showing all relationships
- API documentation auto-generated from OpenAPI spec
- Performance metrics and health status
- Direct links to source code and documentation

**Developer Onboarding Experience**
A new developer can now:
1. Find the task management system in the catalog
2. Understand its purpose and relationships at a glance
3. See all dependencies and APIs it provides/consumes
4. Access comprehensive documentation without reading code
5. Understand the data flow and business logic through the soul.yaml
6. Quickly identify who to contact for questions or changes

## Database-Driven Implementation Example

### Advanced Feature: User Profile Management

This example demonstrates the database-driven approach for a more complex feature involving multiple components and sophisticated validation.

#### Human-Led Planning Phase

**Feature Specification Creation**
```bash
POST /api/components
Content-Type: application/json

{
  "id": "feature:user-profile-management",
  "componentType": "feature",
  "data": {
    "title": "User Profile Management",
    "category": "User Management",
    "description": "Comprehensive user profile creation, editing, and management with validation, image upload, and privacy controls",
    "ownedBy": "User Experience Team",
    "status": "Planning",
    "complexity": "High",
    "estimatedEffort": "3-4 sprints",
    "uses": []
  }
}
```

**Module and Function Discovery**
```bash
# Discover user-related modules
GET /api/components?type=module&category=user
# Returns: module:user-validators, module:user-services, module:profile-handlers

# Explore user services in detail
GET /api/modules/module:user-services/details
```

**Response Example**
```json
{
  "id": "module:user-services",
  "data": {
    "description": "Core user management services",
    "functions": [
      {
        "id": "function:validateUserProfile",
        "signature": "(profile: UserProfile) => ValidationResult",
        "description": "Validates user profile data including email, phone, and personal info",
        "testScenarios": ["valid profile", "invalid email", "missing required fields"]
      },
      {
        "id": "function:uploadProfileImage",
        "signature": "(userId: string, image: File) => Promise<ImageUrl>",
        "description": "Handles profile image upload with resizing and validation",
        "testScenarios": ["valid image", "oversized image", "invalid format"]
      },
      {
        "id": "function:updateUserProfile",
        "signature": "(userId: string, updates: ProfileUpdates) => Promise<User>",
        "description": "Updates user profile with optimistic locking",
        "testScenarios": ["successful update", "concurrent modification", "validation failure"]
      }
    ]
  }
}
```

**Dependency Declaration**
```bash
PATCH /api/components/feature:user-profile-management
Content-Type: application/json

{
  "data": {
    "uses": [
      {"function_id": "function:validateUserProfile"},
      {"function_id": "function:uploadProfileImage"},
      {"function_id": "function:updateUserProfile"},
      {"function_id": "function:checkUserPermissions"},
      {"function_id": "function:auditProfileChange"},
      {"function_id": "function:sendProfileUpdateNotification"}
    ]
  }
}
```

#### AI-Driven Implementation Phase

**Structured Context Preparation**
```javascript
const aiContext = {
  feature: {
    id: "feature:user-profile-management",
    requirements: [
      "Support profile image upload with automatic resizing",
      "Validate all profile fields according to business rules",
      "Implement privacy controls for profile visibility",
      "Audit all profile changes for compliance",
      "Send notifications for significant profile updates"
    ]
  },
  approvedFunctions: [
    {
      id: "function:validateUserProfile",
      module: "module:user-validators",
      signature: "(profile: UserProfile) => ValidationResult",
      usage: "Call before any profile update operation"
    },
    {
      id: "function:uploadProfileImage", 
      module: "module:user-services",
      signature: "(userId: string, image: File) => Promise<ImageUrl>",
      usage: "Handle image upload with automatic resizing"
    }
  ],
  implementationRules: [
    "MUST validate all inputs using approved validation functions",
    "MUST handle file uploads securely with size and type restrictions",
    "MUST implement proper error handling with user-friendly messages",
    "MUST include comprehensive logging for audit purposes",
    "MUST follow existing patterns for database transactions"
  ]
};
```

**AI Implementation Output**
```typescript
// src/features/user-profile/ProfileService.ts
import { validateUserProfile } from '@/validators/user-validators';
import { uploadProfileImage } from '@/services/user-services';
import { updateUserProfile } from '@/repositories/user-repository';
import { checkUserPermissions } from '@/services/auth-service';
import { auditProfileChange } from '@/services/audit-service';
import { sendProfileUpdateNotification } from '@/services/notification-service';

export class ProfileService {
  async updateProfile(
    userId: string,
    profileUpdates: ProfileUpdates,
    requestingUserId: string
  ): Promise<Result<UserProfile, ProfileError>> {
    
    // Check permissions using approved function
    const hasPermission = await checkUserPermissions(
      requestingUserId, 
      'profile:update', 
      userId
    );
    
    if (!hasPermission) {
      return Result.error(new ProfileError('Insufficient permissions'));
    }

    // Validate profile data using approved function
    const validationResult = validateUserProfile(profileUpdates);
    if (!validationResult.isValid) {
      return Result.error(new ProfileError(validationResult.errors));
    }

    try {
      // Handle image upload if provided
      if (profileUpdates.profileImage) {
        const imageUrl = await uploadProfileImage(userId, profileUpdates.profileImage);
        profileUpdates.profileImageUrl = imageUrl;
        delete profileUpdates.profileImage;
      }

      // Update profile using approved function
      const updatedProfile = await updateUserProfile(userId, profileUpdates);

      // Audit the change using approved function
      await auditProfileChange({
        userId,
        changes: profileUpdates,
        updatedBy: requestingUserId,
        timestamp: new Date()
      });

      // Send notification for significant updates
      if (this.isSignificantUpdate(profileUpdates)) {
        await sendProfileUpdateNotification({
          userId,
          updateType: 'profile.updated',
          changes: Object.keys(profileUpdates)
        });
      }

      return Result.success(updatedProfile);
      
    } catch (error) {
      return Result.error(new ProfileError(`Profile update failed: ${error.message}`));
    }
  }

  private isSignificantUpdate(updates: ProfileUpdates): boolean {
    const significantFields = ['email', 'phone', 'name'];
    return significantFields.some(field => field in updates);
  }
}
```

**Automated Validation Results**
```bash
✅ Dependency Analysis: All imports match approved functions
✅ Function Existence: All referenced functions exist in database
✅ Type Safety: TypeScript compilation successful
✅ Security Scan: No vulnerabilities detected
✅ Test Coverage: 96% (above 90% threshold)
✅ Integration Tests: 12/12 passing
✅ Performance Tests: All endpoints under 200ms
```

## Component Integration Scenarios

### Scenario 1: Library Integration

**Context**: Integrating a new email validation library into existing user registration flow.

**Existing Components**
```yaml
# Current user-registration service
spec:
  type: service
  dependsOn:
    - email-validator-v1
```

**Integration Process**
1. **Create new library component**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: email-validator-v2
  description: Enhanced email validation with internationalization support
spec:
  type: library
  lifecycle: production
  owner: platform-team
```

2. **Update dependent service**
```yaml
# Updated user-registration service
spec:
  type: service
  dependsOn:
    - email-validator-v2  # Updated dependency
```

3. **Migration strategy**
```typescript
// Gradual migration approach
import { validateEmail as validateEmailV1 } from '@/lib/email-validator-v1';
import { validateEmail as validateEmailV2 } from '@/lib/email-validator-v2';

export function validateEmail(email: string): ValidationResult {
  // Feature flag for gradual rollout
  if (featureFlags.useEmailValidatorV2) {
    return validateEmailV2(email);
  }
  return validateEmailV1(email);
}
```

### Scenario 2: API Evolution

**Context**: Evolving a REST API to GraphQL while maintaining backward compatibility.

**Original API Component**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-api-rest
spec:
  type: api
  definition:
    $text: ./openapi.yaml
```

**New GraphQL API Component**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-api-graphql
spec:
  type: api
  definition:
    $text: ./schema.graphql
```

**Parallel Operation Strategy**
```typescript
// Gateway service handling both APIs
export class UserApiGateway {
  constructor(
    private restHandler: RestUserHandler,
    private graphqlHandler: GraphQLUserHandler
  ) {}

  async handleRequest(request: Request): Promise<Response> {
    if (request.headers['content-type']?.includes('graphql')) {
      return this.graphqlHandler.handle(request);
    }
    return this.restHandler.handle(request);
  }
}
```

### Scenario 3: Resource Migration

**Context**: Migrating from MySQL to PostgreSQL while maintaining service availability.

**Migration Components**
```yaml
# Original database resource
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: mysql-users-db
spec:
  type: database
  lifecycle: deprecated

---
# New database resource  
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: postgres-users-db
spec:
  type: database
  lifecycle: production
```

**Dual-Write Implementation**
```typescript
export class UserRepository {
  constructor(
    private mysqlConnection: MySQLConnection,
    private postgresConnection: PostgreSQLConnection
  ) {}

  async createUser(userData: UserData): Promise<User> {
    // Write to both databases during migration
    const [mysqlResult, postgresResult] = await Promise.allSettled([
      this.createUserMySQL(userData),
      this.createUserPostgreSQL(userData)
    ]);

    // Verify consistency
    if (mysqlResult.status === 'fulfilled' && postgresResult.status === 'fulfilled') {
      return postgresResult.value; // Prefer new system
    }

    // Handle partial failures
    if (mysqlResult.status === 'fulfilled') {
      // Log PostgreSQL failure for investigation
      logger.error('PostgreSQL write failed during migration', {
        error: postgresResult.reason,
        userData: userData.id
      });
      return mysqlResult.value;
    }

    throw new Error('Both database writes failed');
  }
}
```

## End-to-End Validation Examples

### Validation Level 1: Component Integrity

**Automated Checks**
```bash
# Validate all YAML files
find . -name "*.yaml" -exec yamllint {} \;

# Check catalog-info.yaml completeness
node scripts/validate-catalog-info.js

# Verify soul.yaml structure
node scripts/validate-soul-files.js

# Check cross-references
node scripts/validate-references.js
```

**Example Validation Script**
```javascript
// scripts/validate-catalog-info.js
const fs = require('fs');
const yaml = require('yaml');

function validateCatalogInfo(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const doc = yaml.parse(content);
  
  const required = ['apiVersion', 'kind', 'metadata', 'spec'];
  const missing = required.filter(field => !doc[field]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
  
  // Validate metadata
  if (!doc.metadata.name || !doc.metadata.description) {
    throw new Error('Missing required metadata fields');
  }
  
  // Validate spec based on component type
  validateSpec(doc.spec, doc.kind);
  
  return { valid: true, component: doc };
}
```

### Validation Level 2: System Coherence

**Relationship Validation**
```javascript
// scripts/validate-system-coherence.js
async function validateSystemCoherence(systemName) {
  const components = await findComponentsBySystem(systemName);
  
  // Check for orphaned dependencies
  for (const component of components) {
    if (component.spec.dependsOn) {
      for (const dependency of component.spec.dependsOn) {
        const exists = await componentExists(dependency);
        if (!exists) {
          throw new Error(`Orphaned dependency: ${dependency} in ${component.metadata.name}`);
        }
      }
    }
  }
  
  // Check for circular dependencies
  const dependencyGraph = buildDependencyGraph(components);
  const cycles = findCycles(dependencyGraph);
  if (cycles.length > 0) {
    throw new Error(`Circular dependencies detected: ${cycles.join(', ')}`);
  }
  
  return { valid: true, components: components.length };
}
```

### Validation Level 3: Operational Readiness

**Production Readiness Checklist**
```javascript
// scripts/validate-production-readiness.js
async function validateProductionReadiness(componentName) {
  const component = await getComponent(componentName);
  const checklist = [];
  
  // Health endpoints
  if (component.spec.type === 'service') {
    const healthCheck = await testHealthEndpoint(component);
    checklist.push({
      check: 'Health endpoint',
      status: healthCheck.status === 200 ? 'PASS' : 'FAIL',
      details: healthCheck.details
    });
  }
  
  // Monitoring setup
  const monitoring = await validateMonitoring(component);
  checklist.push({
    check: 'Monitoring configuration',
    status: monitoring.configured ? 'PASS' : 'FAIL',
    details: monitoring.details
  });
  
  // Security scanning
  const security = await runSecurityScan(component);
  checklist.push({
    check: 'Security scan',
    status: security.vulnerabilities === 0 ? 'PASS' : 'WARN',
    details: `${security.vulnerabilities} vulnerabilities found`
  });
  
  return checklist;
}
```

## Best Practices and Anti-Patterns

### Best Practices

1. **Start Simple, Iterate Frequently**
   - Begin with minimal viable documentation
   - Add detail incrementally based on actual needs
   - Regular review and update cycles

2. **Maintain Living Documentation**
   - Update documentation with code changes
   - Automate documentation generation where possible
   - Regular validation and cleanup

3. **Focus on Relationships**
   - Always define component dependencies
   - Document API contracts explicitly
   - Maintain bidirectional references

### Anti-Patterns to Avoid

1. **Documentation Debt**
   ```yaml
   # ❌ Bad: Placeholder content
   metadata:
     description: "TODO: Add description"
   
   # ✅ Good: Meaningful description
   metadata:
     description: "Handles user authentication with JWT tokens and session management"
   ```

2. **Orphaned Dependencies**
   ```yaml
   # ❌ Bad: References non-existent components
   spec:
     dependsOn:
       - non-existent-service
   
   # ✅ Good: All dependencies exist and are documented
   spec:
     dependsOn:
       - postgres-users-db
       - redis-cache
   ```

3. **Missing Operational Context**
   ```yaml
   # ❌ Bad: No operational information
   operationalProfile: {}
   
   # ✅ Good: Complete operational context
   operationalProfile:
     availability: "99.9% SLA"
     scaling: "2-10 pods"
     monitoring: "Prometheus + Grafana"
   ```

## Additional Practical Examples

### Image Processing Feature Walkthrough

This example demonstrates building a comprehensive image processing feature that integrates multiple components and showcases the complete methodology workflow.

#### The Feature Story

**Narrative**: Building an image processing feature that allows users to upload, transform, and manage images with automatic optimization, metadata extraction, and secure storage.

**Components Involved**:
- Web portal for image upload interface
- Image processing library for transformations
- S3 storage resource for file persistence
- Auth service for access control
- Metadata extraction service
- Notification service for processing updates

#### Visual Architecture

```mermaid
graph TD
    A[Web Portal] --> B[Image Upload API]
    B --> C[Auth Service]
    B --> D[Image Processing Service]
    D --> E[Image Processing Library]
    D --> F[Metadata Extractor]
    D --> G[S3 Storage]
    D --> H[Notification Service]
    
    I[Database] --> D
    J[Redis Cache] --> D
```

#### Draft catalog-info.yaml Evolution

**Initial Draft State**:
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: image-processing-service
  description: Image upload and processing service (DRAFT)
  annotations:
    backstage.io/techdocs-ref: dir:.
spec:
  type: service
  lifecycle: experimental
  owner: media-team
  system: content-management
  providesApis:
    - image-processing-api
  consumesApis:
    - auth-api
  dependsOn:
    - s3-images-bucket
```

**Final Production State**:
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: image-processing-service
  description: Production-ready image processing service with automatic optimization and metadata extraction
  annotations:
    backstage.io/techdocs-ref: dir:.
    cortexatlas.io/soul-file: './soul.yaml'
    prometheus.io/scrape: 'true'
    grafana.io/dashboard-id: '12345'
spec:
  type: service
  lifecycle: production
  owner: media-team
  system: content-management
  providesApis:
    - image-processing-api
  consumesApis:
    - auth-api
    - notification-api
  dependsOn:
    - s3-images-bucket
    - redis-cache
    - postgres-metadata-db
```

#### Validation Hook Script Example

```bash
#!/bin/bash
# .cortex/hooks/validate-image-service.sh

echo "🔍 Validating image processing service..."

# Check API endpoints
curl -f http://localhost:3000/health || exit 1
curl -f http://localhost:3000/api/v1/images/formats || exit 1

# Validate image processing capabilities
node scripts/test-image-processing.js || exit 1

# Check storage connectivity
aws s3 ls s3://images-bucket-prod/ || exit 1

# Verify metadata extraction
node scripts/test-metadata-extraction.js || exit 1

echo "✅ Image processing service validation complete"
```

#### The Payoff: Discoverable Architecture

**Backstage Catalog Screenshots Simulation**:
```
📊 Image Processing Service Dashboard
├── 🏥 Health: ✅ Healthy
├── 📈 Performance: 150ms avg response time
├── 🔗 Dependencies: 
│   ├── ✅ S3 Images Bucket (healthy)
│   ├── ✅ Redis Cache (healthy)
│   └── ✅ PostgreSQL Metadata DB (healthy)
├── 🔌 APIs Provided:
│   └── Image Processing API (v2.1.0)
├── 🔌 APIs Consumed:
│   ├── Auth API (v1.3.0)
│   └── Notification API (v1.1.0)
└── 👥 Team: Media Team (@media-team)
```

**Developer Understanding Without Code**:
A new developer can immediately understand:
- The service processes images with automatic optimization
- It requires authentication for all operations
- It stores images in S3 and metadata in PostgreSQL
- It sends notifications about processing status
- The media team owns and maintains it
- All dependencies are healthy and properly connected

### Database-Driven Feature Creation Examples

#### Advanced User Management System

This comprehensive example demonstrates the database-driven approach for building a sophisticated user management system with role-based access control, audit logging, and multi-tenant support.

#### Human-Led Planning Phase

**Epic Decomposition with Database Context**
```bash
# Create the feature in the component database
POST /api/components
{
  "id": "feature:advanced-user-management",
  "componentType": "feature",
  "data": {
    "title": "Advanced User Management System",
    "category": "Identity & Access Management",
    "description": "Multi-tenant user management with RBAC, audit logging, and advanced security features",
    "ownedBy": "Security Team",
    "status": "Planning",
    "complexity": "High",
    "estimatedEffort": "6-8 sprints",
    "businessValue": "Critical - enables enterprise customer onboarding",
    "uses": []
  }
}
```

**Comprehensive Module Discovery**
```bash
# Discover all authentication-related modules
GET /api/components?type=module&category=auth&status=production
# Returns: module:auth-core, module:rbac-engine, module:audit-logger

# Explore RBAC engine capabilities
GET /api/modules/module:rbac-engine/details
```

**Detailed Module Response**:
```json
{
  "id": "module:rbac-engine",
  "data": {
    "description": "Role-based access control engine with hierarchical permissions",
    "version": "2.1.0",
    "maintainer": "Security Team",
    "functions": [
      {
        "id": "function:createRole",
        "signature": "(roleData: RoleDefinition) => Promise<Role>",
        "description": "Creates a new role with specified permissions",
        "complexity": "Medium",
        "testScenarios": [
          "Create basic role with single permission",
          "Create hierarchical role with inherited permissions",
          "Handle duplicate role creation",
          "Validate permission existence"
        ]
      },
      {
        "id": "function:assignUserRole",
        "signature": "(userId: string, roleId: string, tenantId?: string) => Promise<void>",
        "description": "Assigns a role to a user with optional tenant scoping",
        "complexity": "High",
        "testScenarios": [
          "Assign role to user in default tenant",
          "Assign role to user in specific tenant",
          "Handle conflicting role assignments",
          "Validate user and role existence"
        ]
      },
      {
        "id": "function:checkPermission",
        "signature": "(userId: string, permission: string, resource?: string) => Promise<boolean>",
        "description": "Checks if user has specific permission on resource",
        "complexity": "High",
        "testScenarios": [
          "Check direct permission",
          "Check inherited permission",
          "Check resource-specific permission",
          "Handle non-existent user/permission"
        ]
      }
    ],
    "schemas": [
      {
        "name": "RoleDefinition",
        "description": "Schema for defining roles and permissions",
        "fields": ["name", "description", "permissions", "parentRole", "tenantScoped"]
      }
    ]
  }
}
```

**Strategic Dependency Selection**
```bash
# Update feature with carefully selected dependencies
PATCH /api/components/feature:advanced-user-management
{
  "data": {
    "uses": [
      {"function_id": "function:createRole", "rationale": "Core RBAC functionality"},
      {"function_id": "function:assignUserRole", "rationale": "Multi-tenant role assignment"},
      {"function_id": "function:checkPermission", "rationale": "Runtime permission checking"},
      {"function_id": "function:hashPassword", "rationale": "Secure password storage"},
      {"function_id": "function:validateUserInput", "rationale": "Input sanitization"},
      {"function_id": "function:auditUserAction", "rationale": "Compliance logging"},
      {"function_id": "function:sendSecurityNotification", "rationale": "Security event alerts"}
    ]
  }
}
```

#### AI Implementation with Rich Context

**Comprehensive Implementation Context**
```javascript
const advancedImplementationContext = {
  feature: {
    id: "feature:advanced-user-management",
    businessRequirements: [
      "Support multiple tenants with isolated user spaces",
      "Implement hierarchical role-based access control",
      "Provide comprehensive audit logging for compliance",
      "Enable self-service user management for tenant admins",
      "Support SSO integration with enterprise identity providers"
    ],
    technicalConstraints: [
      "Must handle 10,000+ concurrent users",
      "Sub-200ms response time for permission checks",
      "99.9% availability requirement",
      "GDPR and SOX compliance mandatory"
    ]
  },
  approvedDependencies: [
    {
      id: "function:createRole",
      module: "module:rbac-engine",
      usage: "Create tenant-specific roles with inheritance",
      performance: "~50ms average execution time",
      errorHandling: "Returns Result<Role, RoleCreationError>"
    },
    {
      id: "function:checkPermission",
      module: "module:rbac-engine", 
      usage: "High-frequency permission checking with caching",
      performance: "~5ms with Redis cache, ~25ms without",
      errorHandling: "Returns boolean, logs errors internally"
    }
  ],
  architecturalPatterns: {
    "caching": "Redis for permission cache with 5-minute TTL",
    "database": "PostgreSQL with read replicas for permission queries",
    "eventSourcing": "Audit events stored in separate event store",
    "apiDesign": "RESTful with GraphQL for complex queries"
  }
};
```

**Advanced AI Implementation**
```typescript
// src/features/user-management/AdvancedUserService.ts
import { createRole, assignUserRole, checkPermission } from '@/modules/rbac-engine';
import { hashPassword } from '@/modules/auth-core';
import { validateUserInput } from '@/modules/input-validators';
import { auditUserAction } from '@/modules/audit-logger';
import { sendSecurityNotification } from '@/modules/notification-service';

export class AdvancedUserService {
  constructor(
    private cache: RedisClient,
    private eventStore: EventStore,
    private tenantService: TenantService
  ) {}

  async createTenantAdmin(
    tenantId: string,
    adminData: AdminUserData,
    createdBy: string
  ): Promise<Result<User, UserCreationError>> {
    
    // Validate input using approved function
    const validationResult = validateUserInput(adminData);
    if (!validationResult.isValid) {
      return Result.error(new UserCreationError(validationResult.errors));
    }

    // Start database transaction for consistency
    return await this.db.transaction(async (tx) => {
      try {
        // Create tenant-specific admin role if it doesn't exist
        const adminRole = await this.ensureTenantAdminRole(tenantId, tx);
        
        // Hash password using approved function
        const hashedPassword = await hashPassword(adminData.password);
        
        // Create user record
        const user = await tx.users.create({
          ...adminData,
          password: hashedPassword,
          tenantId,
          status: 'active',
          createdBy
        });

        // Assign admin role using approved function
        await assignUserRole(user.id, adminRole.id, tenantId);

        // Audit the action using approved function
        await auditUserAction({
          action: 'user.created',
          userId: user.id,
          tenantId,
          performedBy: createdBy,
          details: {
            userType: 'tenant-admin',
            roleAssigned: adminRole.id
          },
          timestamp: new Date()
        });

        // Send security notification using approved function
        await sendSecurityNotification({
          type: 'tenant-admin-created',
          tenantId,
          userId: user.id,
          severity: 'medium',
          details: 'New tenant administrator account created'
        });

        // Invalidate relevant caches
        await this.invalidateUserCaches(user.id, tenantId);

        return Result.success(user);

      } catch (error) {
        // Comprehensive error handling
        await this.handleUserCreationError(error, adminData, tenantId);
        return Result.error(new UserCreationError(error.message));
      }
    });
  }

  async checkUserPermission(
    userId: string,
    permission: string,
    resource?: string,
    tenantId?: string
  ): Promise<boolean> {
    
    // Check cache first for performance
    const cacheKey = `perm:${userId}:${permission}:${resource || 'global'}:${tenantId || 'default'}`;
    const cached = await this.cache.get(cacheKey);
    
    if (cached !== null) {
      return cached === 'true';
    }

    // Use approved function for permission checking
    const hasPermission = await checkPermission(userId, permission, resource);
    
    // Cache result with TTL
    await this.cache.setex(cacheKey, 300, hasPermission.toString());
    
    // Audit permission check for sensitive permissions
    if (this.isSensitivePermission(permission)) {
      await auditUserAction({
        action: 'permission.checked',
        userId,
        tenantId,
        details: {
          permission,
          resource,
          result: hasPermission
        },
        timestamp: new Date()
      });
    }

    return hasPermission;
  }

  private async ensureTenantAdminRole(tenantId: string, tx: Transaction): Promise<Role> {
    const roleId = `tenant-admin-${tenantId}`;
    
    let role = await tx.roles.findById(roleId);
    if (!role) {
      // Create tenant admin role using approved function
      role = await createRole({
        id: roleId,
        name: 'Tenant Administrator',
        description: `Administrative role for tenant ${tenantId}`,
        permissions: [
          'user.create',
          'user.read',
          'user.update',
          'user.delete',
          'role.assign',
          'tenant.configure'
        ],
        tenantScoped: true,
        tenantId
      });
    }
    
    return role;
  }

  private async invalidateUserCaches(userId: string, tenantId: string): Promise<void> {
    const patterns = [
      `perm:${userId}:*`,
      `user:${userId}:*`,
      `tenant:${tenantId}:users:*`
    ];
    
    for (const pattern of patterns) {
      const keys = await this.cache.keys(pattern);
      if (keys.length > 0) {
        await this.cache.del(...keys);
      }
    }
  }

  private isSensitivePermission(permission: string): boolean {
    const sensitivePermissions = [
      'user.delete',
      'role.assign',
      'tenant.configure',
      'audit.access',
      'system.admin'
    ];
    return sensitivePermissions.includes(permission);
  }
}
```

#### Comprehensive Validation Pipeline

**Multi-Level Validation Results**
```bash
🔍 Static Analysis
✅ Dependency validation: All 7 functions exist and are accessible
✅ Import analysis: Only approved functions imported
✅ Type safety: TypeScript strict mode compilation successful
✅ Security scan: No vulnerabilities detected

🧪 Unit Testing  
✅ Function tests: 45/45 passing
✅ Integration tests: 18/18 passing
✅ Edge case coverage: 92% (above 90% threshold)
✅ Performance tests: All operations under SLA limits

🏗️ Integration Validation
✅ Database transactions: ACID compliance verified
✅ Cache consistency: Redis cache invalidation working
✅ Event sourcing: Audit events properly stored
✅ Multi-tenancy: Tenant isolation verified

🚀 Production Readiness
✅ Load testing: Handles 15,000 concurrent users
✅ Failover testing: Graceful degradation verified
✅ Monitoring: All metrics and alerts configured
✅ Documentation: API docs auto-generated
```

### Complex Component Integration Scenarios

#### Scenario: Microservices Orchestration

**Context**: Building an e-commerce order processing system that orchestrates multiple microservices with complex business logic and failure handling.

**Component Architecture**
```yaml
# Order orchestration system
components:
  - name: order-orchestrator
    type: service
    role: coordinator
  - name: inventory-service
    type: service
    role: domain-service
  - name: payment-service
    type: service
    role: domain-service
  - name: shipping-service
    type: service
    role: domain-service
  - name: notification-service
    type: service
    role: infrastructure
```

**Orchestration Implementation**
```typescript
// Order processing with saga pattern
export class OrderOrchestrator {
  async processOrder(orderData: OrderRequest): Promise<Result<Order, OrderError>> {
    const saga = new OrderProcessingSaga(orderData);
    
    try {
      // Step 1: Reserve inventory
      const inventoryReservation = await this.inventoryService.reserveItems(
        orderData.items
      );
      saga.addCompensation(() => 
        this.inventoryService.releaseReservation(inventoryReservation.id)
      );

      // Step 2: Process payment
      const payment = await this.paymentService.processPayment({
        amount: orderData.total,
        paymentMethod: orderData.paymentMethod
      });
      saga.addCompensation(() => 
        this.paymentService.refundPayment(payment.id)
      );

      // Step 3: Arrange shipping
      const shipment = await this.shippingService.createShipment({
        items: orderData.items,
        address: orderData.shippingAddress
      });
      saga.addCompensation(() => 
        this.shippingService.cancelShipment(shipment.id)
      );

      // Step 4: Create order record
      const order = await this.orderService.createOrder({
        ...orderData,
        inventoryReservationId: inventoryReservation.id,
        paymentId: payment.id,
        shipmentId: shipment.id,
        status: 'confirmed'
      });

      // Step 5: Send confirmation
      await this.notificationService.sendOrderConfirmation(order);

      return Result.success(order);

    } catch (error) {
      // Execute compensating transactions
      await saga.compensate();
      return Result.error(new OrderError(error.message));
    }
  }
}
```

#### Scenario: Event-Driven Architecture

**Context**: Implementing an event-driven system for real-time inventory updates across multiple sales channels.

**Event Flow Architecture**
```mermaid
graph LR
    A[Web Store] --> B[Order Service]
    C[Mobile App] --> B
    D[POS System] --> B
    
    B --> E[Event Bus]
    E --> F[Inventory Service]
    E --> G[Analytics Service]
    E --> H[Notification Service]
    
    F --> I[Warehouse System]
    F --> J[Supplier API]
```

**Event-Driven Implementation**
```typescript
// Event-driven inventory management
export class InventoryEventHandler {
  @EventHandler('order.created')
  async handleOrderCreated(event: OrderCreatedEvent): Promise<void> {
    const { orderId, items } = event.data;
    
    // Update inventory levels
    for (const item of items) {
      await this.inventoryService.decrementStock(item.sku, item.quantity);
      
      // Check if reorder is needed
      const currentStock = await this.inventoryService.getStock(item.sku);
      if (currentStock <= item.reorderPoint) {
        await this.eventBus.publish(new ReorderRequiredEvent({
          sku: item.sku,
          currentStock,
          reorderPoint: item.reorderPoint,
          suggestedQuantity: item.reorderQuantity
        }));
      }
    }
    
    // Publish inventory updated event
    await this.eventBus.publish(new InventoryUpdatedEvent({
      orderId,
      items: items.map(item => ({
        sku: item.sku,
        newStock: currentStock - item.quantity
      }))
    }));
  }

  @EventHandler('inventory.low-stock')
  async handleLowStock(event: LowStockEvent): Promise<void> {
    const { sku, currentStock, reorderPoint } = event.data;
    
    // Notify purchasing team
    await this.notificationService.sendLowStockAlert({
      sku,
      currentStock,
      reorderPoint,
      urgency: currentStock === 0 ? 'critical' : 'medium'
    });
    
    // Auto-reorder if configured
    const product = await this.productService.getProduct(sku);
    if (product.autoReorder) {
      await this.purchasingService.createPurchaseOrder({
        sku,
        quantity: product.reorderQuantity,
        supplier: product.preferredSupplier
      });
    }
  }
}
```

This comprehensive collection of examples demonstrates the Cortex methodology in practice, providing concrete patterns and approaches for successful implementation across various scenarios and component types. From simple feature development to complex microservices orchestration, these examples show how the methodology scales to meet different architectural needs while maintaining consistency and discoverability.