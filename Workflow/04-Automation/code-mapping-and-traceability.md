## Table of Contents

- [Code Mapping and Traceability](#code-mapping-and-traceability)
  - [Overview](#overview)
  - [Problem Statement and Context](#problem-statement-and-context)
    - [Current Challenges](#current-challenges)
    - [Solution Approach](#solution-approach)
  - [Core Concepts and Implementation](#core-concepts-and-implementation)
    - [Bidirectional Linking Architecture](#bidirectional-linking-architecture)
      - [Documentation-to-Code Links](#documentation-to-code-links)
      - [Code-to-Documentation Links](#code-to-documentation-links)
    - [Code Mapping Section Template](#code-mapping-section-template)
    - [**IX. Code Mapping**](#ix-code-mapping)
      - [**Core Implementation Files**](#core-implementation-files)
      - [**Functional Logic Mapping**](#functional-logic-mapping)
      - [**Data Schema Mapping**](#data-schema-mapping)
      - [**Configuration Mapping**](#configuration-mapping)
      - [**Event Schema Mapping**](#event-schema-mapping)
    - [Naming Conventions and Standards](#naming-conventions-and-standards)
      - [File Path Format Standards](#file-path-format-standards)
      - [Mapping Table Structure Standards](#mapping-table-structure-standards)
    - [Code Beacon System](#code-beacon-system)
      - [Beacon File Structure](#beacon-file-structure)
      - [Beacon Validation Script](#beacon-validation-script)
  - [Validation Strategy and Automated Drift Detection](#validation-strategy-and-automated-drift-detection)
    - [Multi-Level Validation Approach](#multi-level-validation-approach)
      - [Level 1: File Existence Validation](#level-1-file-existence-validation)
      - [Level 2: Symbol and Line Range Validation](#level-2-symbol-and-line-range-validation)
      - [Level 3: Semantic Consistency Validation](#level-3-semantic-consistency-validation)
    - [Automated Drift Detection](#automated-drift-detection)
      - [Change Impact Analysis](#change-impact-analysis)
      - [Notification System](#notification-system)
    - [CI/CD Integration Specifications](#cicd-integration-specifications)
      - [Pre-Commit Hook Integration](#pre-commit-hook-integration)
- [.git/hooks/pre-commit](#githookspre-commit)
      - [GitHub Actions Integration](#github-actions-integration)
  - [Benefits Analysis and Value Proposition](#benefits-analysis-and-value-proposition)
    - [For AI Agents and Code Generation](#for-ai-agents-and-code-generation)
    - [For Development Teams](#for-development-teams)
    - [For System Architecture and Understanding](#for-system-architecture-and-understanding)
    - [Quantifiable Benefits](#quantifiable-benefits)
  - [Integration Specifications and Workflow Integration](#integration-specifications-and-workflow-integration)
    - [Documentation Creation Process Enhancement](#documentation-creation-process-enhancement)
      - [Enhanced Workflow Steps](#enhanced-workflow-steps)
      - [Quality Gates](#quality-gates)
    - [Code Change Process Integration](#code-change-process-integration)
      - [Change Impact Workflow](#change-impact-workflow)
    - [Tool Integration Specifications](#tool-integration-specifications)
      - [IDE Integration Requirements](#ide-integration-requirements)
      - [Build System Integration](#build-system-integration)
      - [Documentation Platform Integration](#documentation-platform-integration)
  - [Implementation Timeline and Rollout Strategy](#implementation-timeline-and-rollout-strategy)
    - [Phase 1: Template Enhancement and Foundation (Week 1)](#phase-1-template-enhancement-and-foundation-week-1)
    - [Phase 2: Tooling Integration and CI-Verified POC (Weeks 2-3)](#phase-2-tooling-integration-and-ci-verified-poc-weeks-2-3)
    - [Phase 3: Organization-Wide Rollout (Week 4+)](#phase-3-organization-wide-rollout-week-4)
  - [Example Implementation: Notification Service](#example-implementation-notification-service)
    - [Complete Code Mapping Example](#complete-code-mapping-example)
    - [**IX. Code Mapping**](#ix-code-mapping)
      - [**Core Implementation Files**](#core-implementation-files)
      - [**Functional Logic Mapping**](#functional-logic-mapping)
      - [**Data Schema Mapping**](#data-schema-mapping)
      - [**Configuration Mapping**](#configuration-mapping)
      - [**Event Schema Mapping**](#event-schema-mapping)
      - [**API Contract Mapping**](#api-contract-mapping)
    - [Corresponding Beacon File](#corresponding-beacon-file)

---

# Code Mapping and Traceability

*This document provides comprehensive guidance on implementing bidirectional code-documentation linking systems within the Cortex methodology, ensuring documentation accuracy and enabling automated validation of code-documentation alignment.*

## Overview

Code mapping and traceability addresses a critical gap in the Soul Forge methodology by creating explicit bidirectional links between documentation sections and actual source code implementations. This system ensures documentation remains synchronized with code changes, enables precise AI code generation, and provides developers with instant navigation between specifications and implementations.

The code mapping system transforms static documentation into a living, validated bridge between architectural intent and actual implementation, reducing maintenance overhead while improving system understanding and code review efficiency.

## Problem Statement and Context

### Current Challenges

The existing Soul Forge methodology produces comprehensive documentation but lacks explicit traceability between documentation sections and actual source code, creating several critical challenges:

**Documentation Drift**: No automated way to verify that documented logic matches implemented logic, leading to outdated specifications that mislead developers and AI agents.

**Maintenance Overhead**: When code changes occur, developers have no systematic way to identify which documentation sections require updates, resulting in inconsistent documentation.

**AI Code Generation Limitations**: AI agents lack precise pointers to existing implementations for reference, reducing the accuracy and consistency of generated code.

**Code Review Inefficiency**: Reviewers cannot easily cross-reference code changes with documented specifications, making it difficult to verify implementation correctness.

**Onboarding Friction**: New developers struggle to navigate from high-level documentation to specific implementation details, slowing productivity.

### Solution Approach

The code mapping and traceability system addresses these challenges through:

1. **Explicit Bidirectional Links**: Direct references between documentation sections and source code locations
2. **Automated Validation**: CI/CD integration that detects and flags documentation-code misalignment
3. **Standardized Mapping Formats**: Consistent templates for different types of code-documentation relationships
4. **Drift Detection**: Automated monitoring of code changes that may affect documentation accuracy

## Core Concepts and Implementation

### Bidirectional Linking Architecture

#### Documentation-to-Code Links
Documentation sections include explicit references to source code locations using standardized formats:

```markdown
**Primary Service Class**: `src/services/NotificationService.ts:1-250`
**Event Consumers**: `src/consumers/UserEventConsumer.ts:15-89`
**API Routes**: `src/routes/notifications.ts:1-156`
```

#### Code-to-Documentation Links
Source code includes metadata linking back to documentation sections:

```typescript
/**
 * Handles user notification processing
 * @documentation src/docs/notification-service/index.md#user-notification-processing
 * @specification src/docs/notification-service/index.md#functional-logic-mapping
 */
export class NotificationService {
  // Implementation
}
```

### Code Mapping Section Template

Add this standardized section to every component's `index.md` after the Asynchronous Specification:

```markdown
### **IX. Code Mapping**
*This section provides explicit links between documented functionality and source code implementation.*

#### **Core Implementation Files**
- **Primary Service Class**: `src/services/NotificationService.ts:1-250`
- **Event Consumers**: `src/consumers/UserEventConsumer.ts:15-89`
- **API Routes**: `src/routes/notifications.ts:1-156`
- **Database Models**: `src/models/Notification.ts:1-45`

#### **Functional Logic Mapping**
| Documented Function | Source Location | Key Methods |
|-------------------|-----------------|-------------|
| `GET /notifications/{userId}` Logic Flow | `src/routes/notifications.ts:67-95` | `getNotificationHistory()` |
| User Event Processing | `src/consumers/UserEventConsumer.ts:32-78` | `processUserCreatedEvent()` |
| Email Template Rendering | `src/services/TemplateService.ts:89-134` | `renderWelcomeEmail()` |
| SendGrid Integration | `src/integrations/SendGridClient.ts:23-67` | `sendEmail()`, `handleWebhook()` |

#### **Data Schema Mapping**
| Database Table | Schema Definition | TypeScript Types |
|---------------|-------------------|------------------|
| `notifications` | `migrations/001_create_notifications.sql:1-15` | `src/types/Notification.ts:8-22` |
| `email_templates` | `migrations/002_create_templates.sql:1-12` | `src/types/EmailTemplate.ts:5-18` |

#### **Configuration Mapping**
| Environment Variable | Usage Location | Default Handling |
|--------------------|----------------|------------------|
| `SENDGRID_API_KEY` | `src/config/email.ts:12` | `src/config/email.ts:15-18` |
| `RETRY_MAX_ATTEMPTS` | `src/services/RetryService.ts:8` | `src/services/RetryService.ts:8` |

#### **Event Schema Mapping**
| Event Schema | Type Definition | Producer/Consumer |
|-------------|-----------------|-------------------|
| `user.created.v1` | `src/types/events/UserEvents.ts:15-25` | Consumed in `UserEventConsumer.ts:42` |
| `notification.sent.v1` | `src/types/events/NotificationEvents.ts:8-18` | Produced in `NotificationService.ts:156` |
```

### Naming Conventions and Standards

#### File Path Format Standards
- **Absolute paths** from repository root: `src/services/NotificationService.ts`
- **Line ranges** for specific implementations: `src/services/NotificationService.ts:67-95`
- **Class/method references** where applicable: `NotificationService.sendWelcomeEmail()`
- **Configuration references**: Include both usage location and default handling

#### Mapping Table Structure Standards
Standardized table formats for different mapping types ensure consistency across all documentation:

**Functional Logic Mapping**: Links documented business flows to specific implementation locations
**Data Schema Mapping**: Connects documented data structures to database definitions and TypeScript types
**Configuration Mapping**: Links documented environment variables to actual usage in code
**Event Schema Mapping**: Connects documented event schemas to type definitions and event handlers

### Code Beacon System

#### Beacon File Structure
Create a `.codebeacons.json` file in each component directory to define precise code-documentation links:

```json
{
  "beacons": [
    {
      "path": "src/validators/email-validator.ts",
      "symbol": "validateEmail",
      "lineRange": "23-45",
      "documentation": "index.md#email-validation-logic"
    },
    {
      "path": "src/validators/email-validator.ts",
      "symbol": "validateFormat",
      "lineRange": "47-62",
      "documentation": "index.md#format-validation"
    },
    {
      "path": "src/validators/business-rules.ts",
      "symbol": "applyValidationRules",
      "lineRange": "15-89",
      "documentation": "index.md#business-rule-application"
    }
  ],
  "metadata": {
    "lastUpdated": "2024-01-15T10:30:00Z",
    "version": "1.0",
    "maintainer": "<EMAIL>"
  }
}
```

#### Beacon Validation Script
Implement automated validation through `scripts/verify-codebeacons.js`:

```javascript
const fs = require('fs');
const path = require('path');

function validateCodeBeacons(componentPath) {
  const beaconFile = path.join(componentPath, '.codebeacons.json');
  
  if (!fs.existsSync(beaconFile)) {
    console.warn(`No beacon file found at ${beaconFile}`);
    return true; // Optional for now
  }
  
  const beacons = JSON.parse(fs.readFileSync(beaconFile, 'utf8'));
  let allValid = true;
  
  for (const beacon of beacons.beacons) {
    const filePath = path.join(componentPath, beacon.path);
    
    // Check file existence
    if (!fs.existsSync(filePath)) {
      console.error(`Beacon validation failed: File ${beacon.path} does not exist`);
      allValid = false;
      continue;
    }
    
    // Check symbol existence
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const symbolPattern = new RegExp(`(function|class|const|export).*${beacon.symbol}`, 'g');
    
    if (!symbolPattern.test(fileContent)) {
      console.error(`Beacon validation failed: Symbol ${beacon.symbol} not found in ${beacon.path}`);
      allValid = false;
    }
    
    // Validate line ranges if specified
    if (beacon.lineRange) {
      const lines = fileContent.split('\n');
      const [start, end] = beacon.lineRange.split('-').map(Number);
      
      if (start > lines.length || end > lines.length) {
        console.error(`Beacon validation failed: Line range ${beacon.lineRange} exceeds file length in ${beacon.path}`);
        allValid = false;
      }
    }
  }
  
  return allValid;
}

module.exports = { validateCodeBeacons };
```

## Validation Strategy and Automated Drift Detection

### Multi-Level Validation Approach

#### Level 1: File Existence Validation
- **Automated Check**: Verify that all referenced file paths exist in the repository
- **Frequency**: Every commit via pre-commit hooks
- **Failure Action**: Block commit with specific remediation guidance

#### Level 2: Symbol and Line Range Validation
- **Automated Check**: Validate that referenced symbols exist within specified line ranges
- **Frequency**: CI/CD pipeline execution
- **Failure Action**: Fail build with detailed error reporting

#### Level 3: Semantic Consistency Validation
- **Manual Review**: Verify that documented logic matches implemented logic
- **Frequency**: Code review process and periodic audits
- **Failure Action**: Documentation update requirements

### Automated Drift Detection

#### Change Impact Analysis
Monitor code changes and automatically identify affected documentation:

```javascript
// Example drift detection logic
function detectDocumentationDrift(changedFiles, beaconFiles) {
  const affectedDocs = [];
  
  for (const changedFile of changedFiles) {
    for (const beaconFile of beaconFiles) {
      const beacons = JSON.parse(fs.readFileSync(beaconFile, 'utf8'));
      
      for (const beacon of beacons.beacons) {
        if (beacon.path === changedFile) {
          affectedDocs.push({
            documentation: beacon.documentation,
            changedFile: changedFile,
            beacon: beacon
          });
        }
      }
    }
  }
  
  return affectedDocs;
}
```

#### Notification System
Automatically notify documentation maintainers when code changes affect mapped documentation:

- **Slack/Teams Integration**: Real-time notifications for critical changes
- **GitHub Issues**: Automatic issue creation for documentation updates
- **Email Alerts**: Weekly summaries of accumulated drift

### CI/CD Integration Specifications

#### Pre-Commit Hook Integration
```bash
#!/bin/sh
# .git/hooks/pre-commit
node scripts/verify-codebeacons.js --staged-only
if [ $? -ne 0 ]; then
  echo "Code beacon validation failed. Please update documentation or beacon files."
  exit 1
fi
```

#### GitHub Actions Integration
```yaml
name: Documentation Validation
on: [push, pull_request]

jobs:
  validate-code-mapping:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Validate Code Beacons
        run: |
          node scripts/verify-codebeacons.js --all
          if [ $? -ne 0 ]; then
            echo "::error::Code beacon validation failed"
            exit 1
          fi
```

## Benefits Analysis and Value Proposition

### For AI Agents and Code Generation

**Precise Code Generation**: AI agents can reference exact implementation patterns and architectural decisions, improving generated code quality and consistency.

**Accurate Impact Analysis**: Understanding which files and documentation sections are affected by proposed changes enables better change planning and risk assessment.

**Code-Documentation Consistency**: Automated validation ensures that AI agents work with accurate, up-to-date information rather than outdated specifications.

**Pattern Recognition**: AI can identify and replicate successful implementation patterns across similar components.

### For Development Teams

**Faster Onboarding**: New developers can navigate from high-level documentation to specific implementation details instantly, reducing ramp-up time from weeks to days.

**Efficient Maintenance**: Developers know exactly which documentation sections to update when changing code, eliminating guesswork and reducing maintenance overhead.

**Better Code Reviews**: Reviewers can verify implementation against specifications efficiently, improving review quality while reducing time investment.

**Reduced Context Switching**: Seamless navigation between documentation and code reduces cognitive load and maintains developer flow state.

### For System Architecture and Understanding

**Architecture Visualization**: Clear view of how documented components map to actual code structure enables better architectural decision-making.

**Dependency Tracking**: Understanding implementation-level dependencies helps with refactoring decisions and system evolution planning.

**Legacy Code Documentation**: Retroactively mapping existing code to new documentation standards improves system comprehension and maintainability.

**Compliance and Auditing**: Explicit traceability supports regulatory compliance and technical auditing requirements.

### Quantifiable Benefits

**Documentation Accuracy**: Reduce documentation drift by 80% through automated validation
**Onboarding Time**: Decrease new developer ramp-up time by 60% through improved navigation
**Code Review Efficiency**: Reduce review time by 40% through better specification cross-referencing
**Maintenance Overhead**: Decrease documentation maintenance effort by 50% through targeted update notifications

## Integration Specifications and Workflow Integration

### Documentation Creation Process Enhancement

#### Enhanced Workflow Steps
1. **Write Functional Specification** (existing Soul Forge process)
2. **Implement Code** (existing development process)
3. **Create Code Mapping Section** (new requirement)
   - Reference actual implemented file paths with line ranges
   - Link each documented function to specific source locations
   - Map all schemas to migration files and TypeScript type definitions
   - Document configuration usage and default handling
4. **Generate Code Beacons** (new automation step)
   - Create or update `.codebeacons.json` with precise symbol references
   - Validate beacon accuracy through automated testing
   - Integrate beacon validation into CI/CD pipeline

#### Quality Gates
- **Pre-commit**: Beacon file validation
- **CI/CD**: Comprehensive code mapping validation
- **Code Review**: Manual verification of mapping accuracy
- **Post-deployment**: Periodic drift detection and reporting

### Code Change Process Integration

#### Change Impact Workflow
1. **Make Code Changes** (existing process)
2. **Automated Drift Detection** (new automation)
   - Identify affected documentation through beacon analysis
   - Generate change impact report
   - Notify relevant documentation maintainers
3. **Update Code Mapping** (new requirement)
   - Modify beacon files if file paths or method names changed
   - Update line ranges if implementation structure changed
   - Validate mapping accuracy through automated testing
4. **Review Functional Logic** (enhanced process)
   - Verify that documented logic still matches implementation
   - Update specifications if business logic changed
   - Maintain semantic consistency between docs and code
5. **Commit Integrated Changes** (enhanced process)
   - Commit code and documentation updates together
   - Include mapping updates in the same changeset
   - Validate complete change through CI/CD pipeline

### Tool Integration Specifications

#### IDE Integration Requirements
- **VS Code Extension**: Navigate between documentation and code with keyboard shortcuts
- **IntelliJ Plugin**: Inline documentation references in code editor
- **Vim/Emacs Integration**: Command-line tools for documentation navigation

#### Build System Integration
- **Webpack/Rollup**: Include beacon validation in build process
- **Maven/Gradle**: Integrate validation into Java build lifecycle
- **Make/CMake**: Add validation targets for C/C++ projects

#### Documentation Platform Integration
- **Backstage TechDocs**: Enhanced navigation with code links
- **GitBook/Notion**: Bidirectional linking support
- **Confluence**: Automated synchronization with code changes

## Implementation Timeline and Rollout Strategy

### Phase 1: Template Enhancement and Foundation (Week 1)
**Objectives**: Establish code mapping standards and update core templates

**Deliverables**:
- Enhanced Soul Forge template with Code Mapping section
- Updated methodology documentation with new requirements
- Example implementations for 2-3 existing components
- Initial beacon file format specification

**Success Criteria**:
- All new components include code mapping sections
- Template examples demonstrate all mapping types
- Documentation team trained on new requirements

### Phase 2: Tooling Integration and CI-Verified POC (Weeks 2-3)
**Objectives**: Implement automated validation and demonstrate value with concrete examples

**Target Components for POC**:
1. **Library `email-validator`**
   - `validateEmail()`: `src/validators/email-validator.ts:validateEmail()`
   - `validateFormat()`: `src/validators/email-validator.ts:validateFormat()`
   - `checkDomainExists()`: `src/validators/email-validator.ts:checkDomainExists()`
   - `applyValidationRules()`: `src/validators/business-rules.ts:applyValidationRules()`
   - `buildResult()`: `src/utils/validation-result-builder.ts:buildResult()`

2. **Service `validation-service`**
   - `handleValidate()`: `src/controllers/validationController.ts:handleValidate()`
   - `validateSchema()`: `src/controllers/validationController.ts:validateSchema()`
   - `cache get()`: `src/services/cache.ts:get()`
   - `library wrapper validate()`: `src/services/emailValidationService.ts:validate()`
   - `toResponse()`: `src/controllers/validationController.ts:toResponse()`

**Technical Implementation**:
- Create `.codebeacons.json` files for POC components
- Implement `scripts/verify-codebeacons.js` validation script
- Integrate validation into CI pipeline with failure conditions
- Document remediation procedures for validation failures
- Explore IDE extension options for beacon maintenance

**Success Criteria**:
- CI pipeline fails when beacons are invalid
- Developers can easily update beacons when code changes
- Documentation-code consistency maintained automatically

### Phase 3: Organization-Wide Rollout (Week 4+)
**Objectives**: Apply code mapping to all existing components and establish long-term processes

**Rollout Activities**:
- Apply code mapping process to all existing component documentation
- Train development teams on beacon maintenance procedures
- Establish documentation review processes with mapping validation
- Create troubleshooting guides and support documentation
- Implement monitoring and reporting for mapping health

**Long-term Maintenance**:
- Monthly audits of mapping accuracy across all components
- Quarterly reviews of tooling effectiveness and improvement opportunities
- Annual assessment of benefits realization and process optimization
- Continuous integration of new tools and IDE extensions

**Success Criteria**:
- 100% of components have accurate code mapping
- Documentation drift reduced by target percentages
- Developer satisfaction with navigation and maintenance improved
- AI code generation accuracy and consistency increased

## Example Implementation: Notification Service

### Complete Code Mapping Example

```markdown
### **IX. Code Mapping**
*This section provides explicit links between documented functionality and source code implementation.*

#### **Core Implementation Files**
- **Primary Service**: `src/services/NotificationService.ts:1-180`
- **Kafka Consumer**: `src/consumers/UserEventConsumer.ts:1-120`  
- **REST API**: `src/api/routes/notifications.ts:1-85`
- **Database Layer**: `src/data/NotificationRepository.ts:1-95`
- **Configuration**: `src/config/notification-config.ts:1-45`

#### **Functional Logic Mapping**
| Documented Function | Source Location | Key Methods |
|-------------------|-----------------|-------------|
| GET /notifications/{userId} validation | `src/api/routes/notifications.ts:23-35` | `validateUserAccess()` |
| GET /notifications/{userId} data query | `src/data/NotificationRepository.ts:45-67` | `findByUserId()` |
| user.created.v1 event processing | `src/consumers/UserEventConsumer.ts:67-89` | `handleUserCreated()` |
| Welcome email template rendering | `src/services/TemplateService.ts:34-56` | `renderWelcomeTemplate()` |
| Retry logic for failed notifications | `src/services/RetryService.ts:12-45` | `scheduleRetry()`, `executeRetry()` |

#### **Data Schema Mapping**
| Database Table | Schema Definition | TypeScript Types |
|---------------|-------------------|------------------|
| `notifications` | `migrations/001_create_notifications.sql:1-25` | `src/types/Notification.ts:8-22` |
| `notification_templates` | `migrations/002_create_templates.sql:1-18` | `src/types/Template.ts:5-15` |
| `user_preferences` | `migrations/003_create_preferences.sql:1-15` | `src/types/UserPreferences.ts:3-12` |

#### **Configuration Mapping**
| Environment Variable | Usage Location | Default Handling |
|--------------------|----------------|------------------|
| `KAFKA_BROKERS` | `src/config/kafka-config.ts:8` | `src/config/kafka-config.ts:12-15` |
| `SENDGRID_API_KEY` | `src/config/email-config.ts:12` | `src/config/email-config.ts:15-18` |
| `RETRY_MAX_ATTEMPTS` | `src/services/RetryService.ts:8` | `src/services/RetryService.ts:8` |
| `NOTIFICATION_QUEUE_SIZE` | `src/config/queue-config.ts:15` | `src/config/queue-config.ts:18-20` |

#### **Event Schema Mapping** 
| Event Schema | Type Definition | Handler Location |
|-------------|-----------------|------------------|
| `user.created.v1` consumed | `src/types/events/UserEvents.ts:12-20` | `UserEventConsumer.ts:67` |
| `notification.sent.v1` produced | `src/types/events/NotificationEvents.ts:8-16` | `NotificationService.ts:145` |
| `notification.failed.v1` produced | `src/types/events/NotificationEvents.ts:18-26` | `NotificationService.ts:167` |

#### **API Contract Mapping**
| Endpoint | OpenAPI Definition | Implementation |
|----------|-------------------|----------------|
| `GET /notifications/{userId}` | `api/openapi.yaml:45-67` | `src/api/routes/notifications.ts:23-45` |
| `POST /notifications/send` | `api/openapi.yaml:68-89` | `src/api/routes/notifications.ts:47-78` |
| `DELETE /notifications/{id}` | `api/openapi.yaml:90-105` | `src/api/routes/notifications.ts:80-95` |
```

### Corresponding Beacon File

```json
{
  "beacons": [
    {
      "path": "src/services/NotificationService.ts",
      "symbol": "NotificationService",
      "lineRange": "1-180",
      "documentation": "index.md#primary-service-implementation"
    },
    {
      "path": "src/api/routes/notifications.ts",
      "symbol": "validateUserAccess",
      "lineRange": "23-35",
      "documentation": "index.md#user-access-validation"
    },
    {
      "path": "src/consumers/UserEventConsumer.ts",
      "symbol": "handleUserCreated",
      "lineRange": "67-89",
      "documentation": "index.md#user-event-processing"
    },
    {
      "path": "src/services/TemplateService.ts",
      "symbol": "renderWelcomeTemplate",
      "lineRange": "34-56",
      "documentation": "index.md#email-template-rendering"
    }
  ],
  "metadata": {
    "component": "notification-service",
    "lastUpdated": "2024-01-15T10:30:00Z",
    "version": "2.1",
    "maintainer": "<EMAIL>",
    "validationLevel": "comprehensive"
  }
}
```

This comprehensive code mapping and traceability system creates a living bridge between Soul Forge documentation and actual codebase implementation, ensuring they evolve together and remain synchronized while providing significant benefits to developers, AI agents, and system maintainers.