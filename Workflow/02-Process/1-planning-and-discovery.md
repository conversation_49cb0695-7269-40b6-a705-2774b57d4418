## Table of Contents

- [Planning and Discovery](#planning-and-discovery)
  - [Overview](#overview)
  - [Key Objectives](#key-objectives)
  - [The Problem and Solution](#the-problem-and-solution)
    - [The Challenge](#the-challenge)
    - [The Solution: 4-Phase Discovery Workflow](#the-solution-4-phase-discovery-workflow)
  - [Phase 1: Feature Concept Definition](#phase-1-feature-concept-definition)
    - [Objective](#objective)
    - [Activities](#activities)
    - [Deliverables](#deliverables)
  - [Phase 2: Component Gap Analysis](#phase-2-component-gap-analysis)
    - [Objective](#objective)
    - [Step 2A: Decompose Epic into Required Capabilities](#step-2a-decompose-epic-into-required-capabilities)
    - [Step 2B: Unified Discovery with AI-Powered Search](#step-2b-unified-discovery-with-ai-powered-search)
    - [Step 2C: Tracking Gaps with Initiatives and Scorecards](#step-2c-tracking-gaps-with-initiatives-and-scorecards)
    - [Deliverables](#deliverables)
  - [Phase 3: Automated Gap Analysis and Decision Making](#phase-3-automated-gap-analysis-and-decision-making)
    - [Objective](#objective)
    - [Step 3A: Assess Candidates with Automated Scorecard](#step-3a-assess-candidates-with-automated-scorecard)
    - [Step 3B: Automated Decision and Reporting](#step-3b-automated-decision-and-reporting)
    - [Step 3C: Example Data Outputs](#step-3c-example-data-outputs)
    - [Deliverables](#deliverables)
  - [Phase 4: Component Requirements Definition](#phase-4-component-requirements-definition)
    - [Objective](#objective)
    - [Step 4A: Finalize Component Requirements List](#step-4a-finalize-component-requirements-list)
    - [Step 4B: Define Architecture Handoff Package](#step-4b-define-architecture-handoff-package)
    - [Deliverables](#deliverables)
  - [Discovery Best Practices](#discovery-best-practices)
    - [Effective Search Methodology](#effective-search-methodology)
    - [Common Discovery Challenges](#common-discovery-challenges)
    - [Quality Assurance Measures](#quality-assurance-measures)
  - [Success Metrics](#success-metrics)
  - [Conclusion](#conclusion)

---

# Planning and Discovery

## Overview

The Planning and Discovery phase is the critical foundation of the workflow methodology, ensuring that all development is necessary, well-planned, and leverages existing work to its fullest potential. This phase systematically analyzes feature requests, discovers existing capabilities within the ecosystem, and identifies architectural gaps **before** any design or implementation work begins.

This comprehensive process addresses the critical missing piece in feature creation workflows: determining what components exist, what can be reused, what needs refactoring, and what must be built new. The primary principle is **reuse over reinvention**, achieved through comprehensive discovery processes and formal gap analysis.

## Key Objectives

- Systematically analyze new feature requests and translate ambiguity into concrete requirements
- Discover existing capabilities within the ecosystem through semantic search and exploration
- Identify architectural gaps and make evidence-based decisions on reuse, refactor, or new development
- Prevent unconstrained development that leads to architectural chaos
- Create clear component requirements list for architecture and design phase
- Maintain commitment to grounded, surgical context in all planning activities

## The Problem and Solution

### The Challenge

When planning features like "allow users to change task tag colors," we encounter critical gaps:
- We don't know what components already exist that could be reused
- We need systematic discovery to prevent reinventing existing functionality
- We must make data-driven decisions about reuse vs new development
- We cannot allow unconstrained development that leads to architectural chaos
- We must maintain our commitment to grounded, surgical context

### The Solution: 4-Phase Discovery Workflow

The planning process follows a structured approach that ensures comprehensive analysis before any design begins:

```
Phase 1: Feature Concept Definition →
Phase 2: Component Gap Analysis →
Phase 3: Automated Decision Making →
Phase 4: Component Requirements Definition
```

This workflow prevents unconstrained development while ensuring all necessary discovery and decision-making is completed before moving to the architecture and design phase.

## Phase 1: Feature Concept Definition

### Objective
Define the high-level feature idea, initial scope, and establish the foundational understanding needed for detailed analysis.

### Activities

#### A. Capture Feature Concept
Start with the high-level Epic or feature request and document it in plain language with clear business context.

**Example Feature Record**:
```json
{
  "id": "feature:task-color-customization",
  "componentType": "feature",
  "data": {
    "title": "Task Color Customization",
    "category": "Task Management",
    "description": "Allow users to change the color of task tags for better visual organization",
    "ownedBy": "Product Team",
    "status": "Concept",
    "businessValue": "Improved user experience and visual organization",
    "estimatedComplexity": "Medium"
  }
}
```

#### B. Define Target Personas and Use Cases
- Identify primary and secondary user personas affected by the feature
- Document specific use cases and user journeys
- Establish success criteria at the business level
- Consider edge cases and exceptional scenarios

#### C. Establish Initial Scope Boundaries
- Define what is explicitly included in the feature scope
- Identify what is explicitly excluded to prevent scope creep
- Document assumptions and dependencies
- Set preliminary timeline and resource expectations

### Deliverables
- Feature concept record in Port.io catalog
- Initial scope document with clear boundaries
- User persona and use case documentation
- Business success criteria definition

## Phase 2: Component Gap Analysis

### Objective
Identify what components are needed for the feature, systematically evaluate existing capabilities for reuse potential, and create a comprehensive gap analysis using Port.io's modern developer portal capabilities.

### Step 2A: Decompose Epic into Required Capabilities

Break down the feature into the smallest possible, discrete technical capabilities required to deliver the feature. This translates ambiguity into concrete requirements that can be systematically analyzed.

**Capability Decomposition Process**:

1. **Understand the Goal**: Start with the high-level Epic or feature request
2. **Define Concrete Capabilities**: Break down into specific technical capabilities needed

**Example for "Task Color Customization"**:
```javascript
const requiredCapabilities = [
  "validate color selection against allowed palette",
  "update task color in database", 
  "broadcast color change to UI components",
  "log color change for audit trail",
  "handle color change permissions"
];
```

**Key Principles for Capability Decomposition**:
- Focus on **what** needs to happen, not **how** it will be implemented
- Break down to the smallest discrete, testable capabilities
- Ensure each capability can be independently validated
- Consider edge cases and error scenarios
- Document assumptions and dependencies between capabilities

### Step 2B: Unified Discovery with AI-Powered Search

With a clear list of required capabilities, explore the existing ecosystem to determine what can be reused. Port.io's AI-powered search provides a unified, conversational discovery experience.

**The Core Approach: Ask, Don't Query**

The fundamental principle is to empower developers to ask questions in natural language. Port.io's AI-powered search engine understands the organization's entire software catalog and provides direct, context-rich answers.

**Discovery Process**:
1. **Formulate a question:** Think about what you need in plain English
2. **Ask the search bar:** Type your question into Port.io's central search interface
3. **Review the results:** The AI returns relevant assets and shows relationships between them

**Example Discovery Questions**:

*For finding specific functionality:*
> "Show me all functions for validating user input colors"
> "Are there any packages that handle color palette validation?"

*For understanding relationships and dependencies:*
> "Find services that handle task updates and show me their owners"
> "What are the dependencies for color-related functionality?"

*For exploring by purpose:*
> "What modules are related to task color management?"
> "Show me components that handle UI state synchronization"

*For checking existing workflows:*
> "How do we currently handle task property updates?"
> "What's the standard pattern for validating user preferences?"

### Step 2C: Tracking Gaps with Initiatives and Scorecards

Transform gap analysis from manual assessment into an automated, transparent workflow using Port.io's **Initiatives** and **Scorecards**.

**1. Create a Feature Initiative and Scorecard:**
The list of required capabilities becomes a new **Initiative** within Port.io (e.g., "Initiative: Feature - Task Color Customization"). A corresponding **Scorecard** is created where each required capability becomes a rule.

Example Scorecard Rule:
- **Capability:** "validate color selection against allowed palette"
- **Scorecard Rule:** `Checks if a component exists with the description 'validates color selection' and is related to 'color-palette-service'`

**2. Visualize the Gap Analysis on a Dashboard:**
Port.io automatically applies this scorecard to the entire software catalog, displaying results on a dedicated dashboard:

- **Reuse (Green/Gold):** Components that fully satisfy a rule
- **Refactor (Yellow/Silver):** Components that partially satisfy a rule  
- **Create (Red/Bronze):** Capabilities with no satisfying component (clear gaps)

**3. Drive Action:**
The dashboard becomes the single source of truth, providing:
- Assignment of owners to fill specific gaps
- Status tracking of new component creation
- Automatic updates as new relevant components are cataloged

### Deliverables
- Complete capability decomposition list
- Live **Initiative Dashboard** tracking component and capability coverage
- **Scorecard** defining the rules for feature completion
- Clear, visual breakdown of components to **Reuse**, **Refactor**, or **Create**

## Phase 3: Automated Gap Analysis and Decision Making

### Objective
Transform gap analysis into a fast, consistent, and transparent workflow executed entirely within Port.io, making data-driven decisions on the path forward for each required capability.

### Step 3A: Assess Candidates with Automated Scorecard

Use an automated **"Component Fitness" Scorecard** that runs against candidate components and assesses their quality based on objective, verifiable data from the software catalog.

**Component Fitness Scorecard Criteria:**

| Category | Rule Examples | Data Source |
| :--- | :--- | :--- |
| **Specification Completeness** | - Owner is defined<br>- Description exists<br>- Source code repository is linked | Port.io Catalog |
| **Code Quality** | - Test coverage > 80%<br>- Zero "Blocker" vulnerabilities<br>- Documentation URL valid | CI Tool / SonarQube<br>Security Scanner<br>Port.io Automation |
| **Production Readiness** | - Component status is "Production"<br>- Used by at least one other "Production" service | Port.io Catalog<br>Port.io Catalog |

### Step 3B: Automated Decision and Reporting

Scorecard results trigger **Port.io Automation** that makes decisions and updates the live **Initiative Dashboard**:

**Automated Workflow:**
1. **Scoring:** Component Fitness Scorecard runs on candidate components
2. **Decision:** Based on score, automation assigns status:
   - **High Score (>90%):** Tagged **"Reuse"**
   - **Medium Score (60-90%):** Tagged **"Refactor"** with auto-created ticket
   - **Low Score (<60%):** Tagged **"Create"** (clear architectural gap)
3. **Reporting:** Initiative Dashboard updates in real-time

### Step 3C: Example Data Outputs

**Example Scorecard Result:**
```json
{
  "component": "function:validateUserInput",
  "capability": "validate color selection against allowed palette",
  "scorecard": "Component Fitness",
  "score": 78,
  "results": {
    "specificationCompleteness": { "score": 100, "passed": true },
    "codeQuality": { "score": 60, "passed": false, "failures": ["Test coverage is 55%"] },
    "productionReadiness": { "score": 100, "passed": true }
  },
  "recommendation": "refactor"
}
```

**Example Decision Documentation:**
```json
{
  "capability": "validate color selection against allowed palette",
  "decision": "refactor",
  "assessedComponent": "function:validateUserInput",
  "rationale": "Component failed 'Code Quality' rules. Test coverage is 55%.",
  "refactoringTicket": "https://your-jira.com/browse/PROJ-123"
}
```

### Deliverables
- Configured **Component Fitness Scorecard** in Port.io
- **Automated Workflow** assigning status based on scorecard results
- Live **Initiative Dashboard** showing real-time status of all required capabilities

## Phase 4: Component Requirements Definition

### Objective
Create the definitive handoff package for the architecture and design phase, containing clear component requirements and decisions for each identified capability.

### Step 4A: Finalize Component Requirements List

Based on the automated analysis, create the final component requirements list that will drive the architecture phase:

**Component Requirements Format:**
```json
{
  "featureId": "task-color-customization",
  "componentRequirements": {
    "reuse": [
      {
        "capability": "update task color in database",
        "component": "function:updateTaskInDatabase",
        "score": 92,
        "rationale": "Existing component fully meets requirements"
      }
    ],
    "refactor": [
      {
        "capability": "validate color selection against allowed palette",
        "component": "function:validateUserInput", 
        "score": 78,
        "requiredChanges": "Add color palette validation support",
        "refactoringTicket": "https://your-jira.com/browse/PROJ-123"
      }
    ],
    "create": [
      {
        "capability": "broadcast color change to UI components",
        "rationale": "No existing component handles UI state synchronization for color changes",
        "priority": "high",
        "estimatedComplexity": "medium"
      },
      {
        "capability": "log color change for audit trail",
        "rationale": "No existing audit logging for color-specific events",
        "priority": "low", 
        "estimatedComplexity": "low"
      }
    ]
  }
}
```

### Step 4B: Define Architecture Handoff Package

Create comprehensive documentation that provides everything the architecture team needs to begin detailed design work:

**Handoff Package Contents:**
1. **Feature Context**: Business requirements and success criteria
2. **Capability Analysis**: Complete breakdown of required capabilities
3. **Component Decisions**: Final reuse/refactor/create decisions with rationale
4. **Discovery Evidence**: Search results and analysis that led to decisions
5. **Constraints and Requirements**: Business rules, performance requirements, security considerations
6. **Integration Requirements**: How new components must work with existing systems

### Deliverables
- Finalized component requirements list with clear decisions
- Comprehensive architecture handoff package
- Updated Initiative Dashboard with planning phase completion
- Clear transition criteria for moving to architecture and design phase

## Discovery Best Practices

### Effective Search Methodology

**4-Step Method for Crafting Search Queries:**

**Step 1: Identify the Core Domain (The Noun)**
Start by asking: "What part of the system am I working in?"
- Feature: "Task Color Customization" → **Domain:** `task`
- Feature: "Cancel subscription" → **Domain:** `subscription` 

**Step 2: Identify the Core Action (The Verb)**
Ask: "What is the fundamental action I am trying to perform?"
- Feature: "Task Color Customization" → **Action:** `update` or `change`
- Your first search: *"search for `task update`"*

**Step 3: Decompose into Specific Capabilities**
Connect back to your capability decomposition. Use capabilities as search keywords:
- `validate color selection` → **Keywords:** `validate`, `color`, `selection`
- Combined search: *"search for components related to `task` `updates` and `validation`"*

**Step 4: Think in Synonyms and Abstractions**
If initial searches don't yield results, broaden your terms:
- Instead of `update`, try: `modify`, `set`, `save`, `change`
- Instead of `validate`, try: `check`, `verify`, `rule`, `constraint`

### Common Discovery Challenges

#### Outdated or Incomplete Catalog Data
**Problem**: Software catalog doesn't perfectly reflect reality
**Solutions**:
- Configure Port.io to automatically ingest catalog data from CI/CD pipelines
- Use Port.io scorecards to audit catalog completeness
- Create initiatives to drive cataloging of unregistered components

#### Inconsistent Naming and Properties  
**Problem**: Teams use different property names (`service-tier` vs `tier`)
**Solutions**:
- Define clear, consistent data model (Blueprint) in Port.io
- Enforce required properties and use dropdowns for standardized values
- Use scorecards to identify components not adhering to standards

#### Poorly Defined Relationships
**Problem**: Missing dependency and integration context
**Solutions**:
- Make relationship mapping required in component definition process
- Use Port.io's graph visualization to explore and validate relationships
- Define relations in Blueprints as required fields

### Quality Assurance Measures

#### Search Quality Validation
- **Hit Rate Tracking**: Monitor percentage of searches finding relevant components
- **Precision Measurement**: Evaluate relevance of returned results
- **User Feedback Integration**: Collect feedback on search result quality

#### Gap Analysis Validation  
- **Peer Review Process**: Have gap analysis reviewed by senior developers
- **Architecture Alignment Check**: Ensure decisions align with system architecture
- **Cost-Benefit Analysis**: Validate refactoring/new development costs are justified

## Success Metrics

### Planning Effectiveness
- **Capability Coverage**: Percentage of feature requirements covered by identified components
- **Reuse Rate**: Percentage of capabilities satisfied by existing components  
- **Decision Accuracy**: Percentage of reuse/refactor/create decisions that prove correct

### Discovery Quality
- **Search Success Rate**: Percentage of capability searches that find relevant components
- **Component Utilization**: Percentage of discovered components actually used
- **Discovery Completeness**: Percentage of relevant existing components found

### Process Efficiency
- **Analysis Cycle Time**: Time from feature concept to completed gap analysis
- **Rework Rate**: Percentage of decisions requiring revision
- **Stakeholder Satisfaction**: Feedback from development teams on analysis quality

## Conclusion

The Planning and Discovery phase establishes the foundation for all subsequent development work by ensuring comprehensive understanding of existing capabilities and genuine architectural needs. Through systematic capability decomposition, AI-powered discovery using Port.io's unified search, automated gap analysis with scorecards and initiatives, and evidence-based decision making, this phase prevents architectural drift while maximizing reuse and maintaining system coherence.

**Key Strategic Benefits:**
- **Prevents Reinvention**: Systematic discovery ensures existing capabilities are found and reused
- **Data-Driven Decisions**: Automated scorecards provide objective assessment of component fitness
- **Maintains Architecture Integrity**: Evidence-based gap analysis prevents architectural chaos
- **Enables Surgical Precision**: Grounded analysis ensures only necessary work is undertaken

The phase concludes with a definitive component requirements list that provides clear direction for the architecture and design phase, ensuring all subsequent work is grounded in evidence-based decisions and comprehensive system understanding. This approach scales beautifully as applications grow in complexity while maintaining precision in component analysis and architectural decision-making.

**Handoff to Architecture Phase**: The planning phase delivers a complete analysis of what exists, what can be reused, what needs refactoring, and what must be created new. This becomes the foundation for detailed component specification and architectural design in the next phase.