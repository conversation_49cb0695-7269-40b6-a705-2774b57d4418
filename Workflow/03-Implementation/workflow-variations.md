# Workflow Variations Guide

> **Purpose:** This comprehensive guide provides specialized approaches for documenting different component types using the Cortex methodology. Each variation is optimized for the unique characteristics, concerns, and stakeholder needs of specific component types.

## Table of Contents

### [Component Type Overview](#component-type-overview)
- [Decision Matrix for Component Types](#decision-matrix-for-component-types)
- [Common Patterns Across Types](#common-patterns-across-types)

### [Service Components](#service-components)
- [Standard Services](#standard-services)
- [Microservices](#microservices)
- [Event-Driven Services](#event-driven-services)
- [Batch Processing Services](#batch-processing-services)

### [Library Components](#library-components)
- [Utility Libraries](#utility-libraries)
- [SDK Libraries](#sdk-libraries)
- [Framework Libraries](#framework-libraries)
- [Internal Shared Libraries](#internal-shared-libraries)

### [API Components](#api-components)
- [REST APIs](#rest-apis)
- [GraphQL APIs](#graphql-apis)
- [gRPC Services](#grpc-services)
- [Event APIs](#event-apis)

### [Resource Components](#resource-components)
- [Database Resources](#database-resources)
- [Cache Resources](#cache-resources)
- [Message Queue Resources](#message-queue-resources)
- [Storage Resources](#storage-resources)

### [Website Components](#website-components)
- [Single Page Applications](#single-page-applications)
- [Server-Side Rendered Applications](#server-side-rendered-applications)
- [Static Sites](#static-sites)
- [Mobile Applications](#mobile-applications)

### [System Components](#system-components)
- [Business Domain Systems](#business-domain-systems)
- [Platform Systems](#platform-systems)
- [Integration Systems](#integration-systems)

### [Specialized Variations](#specialized-variations)
- [Legacy System Documentation](#legacy-system-documentation)
- [Third-Party Integration Documentation](#third-party-integration-documentation)
- [Experimental Component Documentation](#experimental-component-documentation)

### [Decision Framework](#decision-framework)
- [Choosing the Right Variation](#choosing-the-right-variation)
- [Hybrid Approaches](#hybrid-approaches)
- [Evolution Paths](#evolution-paths)

## Quick Reference: Simplified Workflows

### Time-Based Workflow Selection

| Time Available | Component Type | Workflow Approach | Key Focus |
|----------------|----------------|-------------------|-----------|
| **15-30 minutes** | Library | Simplified | Function registry with examples |
| **20-30 minutes** | API | Contract-First | OpenAPI spec reference |
| **15-25 minutes** | Resource | Infrastructure | Connection details and schemas |
| **30-45 minutes** | Service | Standard | Operational profile and monitoring |
| **45-60 minutes** | Website | Full | Performance metrics and UX |
| **60-90 minutes** | System | Comprehensive | Business alignment and component relationships |

### Simplified Workflow Patterns

**For Libraries (15-30 minutes):**
- Focus on function registry in soul.yaml
- Provide clear usage examples in index.md
- Emphasize compatibility and integration patterns

**For APIs (20-30 minutes):**
- Reference existing OpenAPI specification
- Focus on contract metadata and versioning
- Explain design decisions and consumer guidance

**For Resources (15-25 minutes):**
- Document connection methods and access control
- Include basic schema information
- Provide operational procedures and backup policies

## Component Type Overview

### Decision Matrix for Component Types

| Characteristic | Service | Library | API | Resource | Website | System |
|---------------|---------|---------|-----|----------|---------|--------|
| **Deployment** | Independent | Embedded | Contract | Managed | Hosted | Conceptual |
| **Runtime** | Always running | On-demand | Interface | Infrastructure | User-facing | Logical grouping |
| **Consumers** | Other services | Developers | Applications | Services | End users | Multiple components |
| **Versioning** | Semantic | Semantic | Contract-based | Schema-based | Release-based | Capability-based |
| **Testing** | Integration | Unit + Integration | Contract | Operational | E2E + Visual | System-level |
| **Monitoring** | APM + Metrics | Usage analytics | SLA monitoring | Infrastructure | RUM + Core Vitals | Business metrics |

### Common Patterns Across Types

**Universal Elements (All Types):**
- Three essential files (catalog-info.yaml, soul.yaml, index.md)
- AI Context Header with component-specific metadata
- Foundation dimension with governance and identity
- Cross-references to related components

**Type-Specific Focus Areas:**
- **Services:** Operational profile, performance, scaling
- **Libraries:** Function registry, usage patterns, compatibility
- **APIs:** Contract evolution, consumer guidance, SLA management
- **Resources:** Connection details, capacity planning, operational procedures
- **Websites:** User experience, performance metrics, accessibility
- **Systems:** Component relationships, data flow, business alignment

## Service Components### 
Standard Services

**Focus Areas:** Operational excellence, performance monitoring, service reliability

**Enhanced catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: auth-service
  description: User authentication service with JWT token management
  labels:
    system: user-management
    tier: critical
    service-type: stateless
  annotations:
    backstage.io/techdocs-ref: dir:.
    cortexatlas.io/soul-file: './soul.yaml'
    prometheus.io/scrape: 'true'
    prometheus.io/path: '/metrics'
    prometheus.io/port: '8080'
spec:
  type: service
  lifecycle: production
  owner: team:default/platform-team
  system: user-management
  providesApis:
    - auth-api
  consumesApis:
    - user-profile-api
  dependsOn:
    - resource:default/postgres-auth-db
    - resource:default/redis-sessions
```

**Service-Specific soul.yaml:**
```yaml
entityMetadata:
  uuid: "550e8400-e29b-41d4-a716-************"
  created: "2024-01-15"
  version: "2.1.3"
  serviceType: "stateless-microservice"

operationalProfile:
  availability:
    target: "99.9%"
    measurement: "Monthly uptime excluding planned maintenance"
  deploymentPlatform: "Kubernetes on AWS EKS"
  runtime:
    language: "Node.js"
    version: "18.x"
    framework: "Express.js"
    containerImage: "auth-service:2.1.3"
  scaling:
    strategy: "horizontal"
    min: 2
    max: 10
    triggers:
      - metric: "CPU"
        threshold: "70%"
      - metric: "Memory"
        threshold: "80%"
      - metric: "Request Rate"
        threshold: "100 RPS"
  monitoringEndpoints:
    health: "/health"
    metrics: "/metrics"
    readiness: "/ready"
    liveness: "/live"

performanceProfile:
  expectedLatency:
    p50: "< 50ms"
    p95: "< 200ms"
    p99: "< 500ms"
  throughput:
    sustained: "100 RPS"
    peak: "500 RPS"
    tested: "1000 RPS"
  resourceLimits:
    cpu: "500m"
    memory: "512Mi"
    storage: "1Gi"
  loadTesting:
    lastRun: "2024-01-10"
    tool: "k6"
    scenarios: ["normal-load", "spike-test", "stress-test"]

securityProfile:
  dataClassification: "PII-Sensitive"
  authentication: "JWT tokens with RS256 signing"
  authorization: "Role-based access control (RBAC)"
  encryption:
    atRest: "AES-256 via AWS KMS"
    inTransit: "TLS 1.3"
  compliance:
    - "SOC2 Type II"
    - "GDPR Article 32"
  auditLogging: true
  threatModel:
    - threat: "Brute force attacks"
      mitigation: "Rate limiting (5 attempts/minute/IP)"
    - threat: "Token theft"
      mitigation: "Short expiration (15 minutes)"
    - threat: "Session hijacking"
      mitigation: "Secure cookies with HttpOnly flag"

businessProfile:
  criticality: "tier-1"
  businessValue: "Enables secure access to all customer applications"
  userImpact: "10,000 daily active users"
  revenueImpact: "$10M+ ARR dependency"
  downtimeCost: "$10,000 per hour"
```

**Service-Specific AI Context:**
```json
{
  "aiContext": {
    "entity": {
      "kind": "Component",
      "name": "auth-service",
      "type": "service",
      "servicePattern": "stateless-microservice"
    },
    "runtime": {
      "platform": "kubernetes",
      "language": "nodejs",
      "framework": "express"
    },
    "contracts": {
      "providesApis": [
        {
          "name": "api:default/auth-api",
          "version": "v1",
          "protocol": "REST",
          "format": "OpenAPI 3.0"
        }
      ],
      "consumesApis": [
        {
          "name": "api:default/user-profile-api",
          "version": "v2",
          "purpose": "Fetch user profile data for token enrichment"
        }
      ],
      "publishesEvents": [
        {
          "name": "user.logged.in",
          "schema": "UserLoginEvent",
          "frequency": "per login attempt"
        }
      ]
    },
    "operationalFlows": [
      {
        "name": "user-authentication",
        "description": "Complete user login flow with validation",
        "steps": [
          "validate-request-format",
          "check-rate-limit",
          "validate-credentials",
          "check-account-status",
          "generate-jwt-token",
          "create-session-record",
          "emit-login-event",
          "return-token-response"
        ],
        "errorHandling": [
          "invalid-credentials → 401 with retry guidance",
          "rate-limit-exceeded → 429 with backoff time",
          "account-locked → 423 with unlock instructions"
        ]
      }
    ],
    "codeBeacons": {
      "mainEntry": "src/main.ts",
      "handlers": "src/handlers/auth/",
      "businessLogic": "src/services/",
      "dataAccess": "src/repositories/",
      "middleware": "src/middleware/",
      "tests": "tests/",
      "config": "config/",
      "migrations": "migrations/",
      "dockerfile": "Dockerfile",
      "k8sManifests": "k8s/"
    }
  }
}
```###
 Microservices

**Focus Areas:** Service mesh integration, distributed tracing, inter-service communication

**Microservice-Specific Enhancements:**
```yaml
# Additional annotations for microservices
metadata:
  annotations:
    service-mesh.io/inject: 'true'
    jaeger.io/trace: 'true'
    circuit-breaker.io/enabled: 'true'
```

**Microservice soul.yaml additions:**
```yaml
microserviceProfile:
  serviceMesh:
    enabled: true
    provider: "Istio"
    features: ["traffic-management", "security", "observability"]
  
  distributedTracing:
    enabled: true
    provider: "Jaeger"
    samplingRate: "0.1"
  
  circuitBreaker:
    enabled: true
    failureThreshold: 5
    timeout: "30s"
    fallbackStrategy: "return-cached-response"
  
  serviceDiscovery:
    mechanism: "Kubernetes DNS"
    healthCheckInterval: "10s"
```

### Event-Driven Services

**Focus Areas:** Event contracts, message handling, async processing patterns

**Event-Driven soul.yaml:**
```yaml
eventProfile:
  eventBus:
    provider: "Apache Kafka"
    cluster: "production-kafka"
  
  publishedEvents:
    - eventType: "user.registered"
      schema: "UserRegisteredEvent"
      frequency: "per registration"
      retention: "30 days"
    - eventType: "user.profile.updated"
      schema: "UserProfileUpdatedEvent"
      frequency: "per profile change"
      retention: "7 days"
  
  subscribedEvents:
    - eventType: "payment.completed"
      handler: "handlePaymentCompleted"
      retryPolicy: "exponential-backoff"
      deadLetterQueue: "payment-dlq"
  
  processingGuarantees:
    delivery: "at-least-once"
    ordering: "per-partition"
    idempotency: "message-deduplication"
```

### Batch Processing Services

**Focus Areas:** Job scheduling, data processing patterns, resource management

**Batch Processing soul.yaml:**
```yaml
batchProfile:
  scheduler:
    type: "Kubernetes CronJob"
    schedule: "0 2 * * *"  # Daily at 2 AM
    timezone: "UTC"
  
  processingPattern:
    type: "ETL"
    inputSources:
      - "s3://data-lake/raw/"
      - "postgres://analytics-db/events"
    outputTargets:
      - "s3://data-lake/processed/"
      - "postgres://analytics-db/aggregates"
  
  resourceRequirements:
    cpu: "2000m"
    memory: "4Gi"
    storage: "10Gi"
    maxDuration: "4 hours"
  
  dataVolume:
    typical: "100GB per run"
    peak: "500GB per run"
    retention: "90 days"
```

## Library Components

### Utility Libraries

**Focus Areas:** Function registry, usage examples, compatibility matrix

**Simplified Library Workflow (15-30 minutes):**

1. **catalog-info.yaml:** Focus on type: library
2. **soul.yaml:** Emphasize function registry
3. **index.md:** Provide usage examples

**Library catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: date-utilities
  description: Comprehensive date manipulation and formatting utilities
  labels:
    library-type: utility
    language: typescript
spec:
  type: library
  lifecycle: production
  owner: team:default/platform-team
  system: shared-utilities
```

**Simplified Library soul.yaml:**
```yaml
# soul.yaml for libraries
functions:
  validateEmail:
    signature: "(email: string): boolean"
    description: "Validates email format"
    example: "validateEmail('<EMAIL>') // true"
  
  hashPassword:
    signature: "(password: string): string"
    description: "Bcrypt hash with salt rounds=10"
    example: "hashPassword('secretpass') // $2b$10$..."
```

**Comprehensive Library soul.yaml:**
```yaml
libraryProfile:
  packageManager: "npm"
  registry: "https://npm.company.com"
  packageName: "@company/date-utilities"
  currentVersion: "2.3.1"
  
  distributionProfile:
    bundleSize: "12KB minified"
    dependencies: "zero runtime dependencies"
    compatibility:
      node: ">=16.0.0"
      browsers: "ES2020+"
      typescript: ">=4.5.0"
  
  functions:
    formatDate:
      signature: "(date: Date, format: string): string"
      description: "Formats date according to specified pattern"
      example: |
        formatDate(new Date(), 'YYYY-MM-DD')
        // Returns: '2024-01-15'
      performance: "< 1ms average execution time"
    
    parseDate:
      signature: "(dateString: string, format?: string): Date"
      description: "Parses date string with optional format specification"
      example: |
        parseDate('2024-01-15', 'YYYY-MM-DD')
        // Returns: Date object
      errorHandling: "Throws DateParseError for invalid inputs"
    
    addDays:
      signature: "(date: Date, days: number): Date"
      description: "Adds specified number of days to date"
      example: |
        addDays(new Date('2024-01-15'), 7)
        // Returns: Date for 2024-01-22
      immutability: "Returns new Date object, original unchanged"

usagePatterns:
  - pattern: "Date formatting in React components"
    frequency: "Very common"
    example: "formatDate(user.createdAt, 'MMM DD, YYYY')"
  
  - pattern: "API request/response date handling"
    frequency: "Common"
    example: "parseDate(response.timestamp, 'ISO')"
  
  - pattern: "Business logic date calculations"
    frequency: "Common"
    example: "addDays(startDate, subscription.durationDays)"
```

### SDK Libraries

**Focus Areas:** API client generation, authentication handling, error management

**SDK soul.yaml:**
```yaml
sdkProfile:
  targetAPI: "api:default/payment-api"
  apiVersion: "v2.1"
  
  clientFeatures:
    - "Automatic authentication token management"
    - "Request/response type safety"
    - "Built-in retry logic with exponential backoff"
    - "Comprehensive error handling"
    - "Request/response logging"
  
  authenticationMethods:
    - type: "API Key"
      headerName: "X-API-Key"
    - type: "OAuth 2.0"
      flows: ["client-credentials", "authorization-code"]
  
  errorHandling:
    retryPolicy:
      maxAttempts: 3
      backoffStrategy: "exponential"
      retryableErrors: [429, 500, 502, 503, 504]
    
    errorTypes:
      - "PaymentAPIError"
      - "AuthenticationError"
      - "RateLimitError"
      - "ValidationError"
  
  codeGeneration:
    source: "OpenAPI specification"
    generator: "openapi-generator"
    customTemplates: "templates/typescript-client"
```

## API Components

### REST APIs

**Focus Areas:** OpenAPI specification, versioning strategy, consumer guidance

**Simplified API Workflow (Contract-First, 20-30 minutes):**

1. **catalog-info.yaml:** Reference OpenAPI spec
2. **soul.yaml:** Focus on contract metadata
3. **index.md:** Explain design decisions

**Simplified API catalog-info.yaml:**
```yaml
# catalog-info.yaml for APIs
spec:
  type: openapi
  lifecycle: production
  owner: api-team
  definition:
    $text: ./openapi.yaml
```

**Comprehensive API catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: payment-api
  description: Payment processing API with comprehensive transaction management
  labels:
    api-type: rest
    api-version: v2
spec:
  type: openapi
  lifecycle: production
  owner: team:default/payments-team
  system: payment-processing
  definition:
    $text: ./openapi.yaml
```

**API-Specific soul.yaml:**
```yaml
apiProfile:
  specification: "OpenAPI 3.0.3"
  version: "v2.1.0"
  stability: "stable"
  
  versioningStrategy:
    scheme: "URL path versioning"
    pattern: "/api/v{major}/"
    deprecationPolicy: "6 months notice for breaking changes"
    backwardCompatibility: "Maintained for 2 major versions"
  
  endpoints:
    "/payments":
      methods: ["GET", "POST"]
      rateLimit: "100 requests/minute/user"
      authentication: "Bearer token required"
      successRate: "99.5%"
      avgLatency: "150ms"
    
    "/payments/{id}":
      methods: ["GET", "PUT", "DELETE"]
      rateLimit: "200 requests/minute/user"
      authentication: "Bearer token required"
      successRate: "99.8%"
      avgLatency: "75ms"
  
  consumerProfile:
    primaryConsumers:
      - name: "Web Application"
        type: "SPA"
        usage: "High volume, real-time"
      - name: "Mobile Apps"
        type: "Native"
        usage: "Medium volume, offline-capable"
      - name: "Partner Integrations"
        type: "Server-to-server"
        usage: "Batch processing"
    
    sdks:
      - language: "JavaScript/TypeScript"
        package: "@company/payment-client"
        version: "2.1.0"
      - language: "Python"
        package: "company-payment-sdk"
        version: "2.1.0"
  
  contractEvolution:
    breakingChangeProcess:
      - "RFC creation and review"
      - "Stakeholder consultation"
      - "6-month deprecation notice"
      - "Migration guide publication"
      - "Gradual rollout"
    
    nonBreakingChanges:
      - "New optional fields"
      - "New endpoints"
      - "Enhanced error messages"
      - "Performance improvements"
```### GraphQ
L APIs

**Focus Areas:** Schema evolution, query complexity, resolver performance

**GraphQL soul.yaml:**
```yaml
graphqlProfile:
  schemaVersion: "2024.01.15"
  schemaEvolution:
    strategy: "Schema-first development"
    deprecationPolicy: "Field-level deprecation with 6-month lifecycle"
    versioningApproach: "Single evolving schema"
  
  queryComplexity:
    maxDepth: 10
    maxComplexity: 1000
    timeout: "30 seconds"
    costAnalysis: "enabled"
  
  resolverPerformance:
    dataLoaderPattern: "enabled"
    n1Problem: "mitigated via batching"
    cachingStrategy: "Redis with 5-minute TTL"
  
  introspection:
    productionEnabled: false
    developmentEnabled: true
    playgroundEnabled: true
  
  subscriptions:
    transport: "WebSocket"
    authentication: "JWT token in connection params"
    maxConnections: 1000
    heartbeatInterval: "30s"
```

### gRPC Services

**Focus Areas:** Protocol buffer definitions, service mesh integration, streaming

**gRPC soul.yaml:**
```yaml
grpcProfile:
  protoVersion: "proto3"
  packageName: "company.payments.v1"
  
  services:
    PaymentService:
      methods:
        - name: "CreatePayment"
          type: "unary"
          inputType: "CreatePaymentRequest"
          outputType: "CreatePaymentResponse"
        - name: "StreamPaymentUpdates"
          type: "server-streaming"
          inputType: "PaymentStreamRequest"
          outputType: "PaymentUpdate"
  
  clientGeneration:
    languages: ["go", "java", "python", "typescript"]
    outputPath: "generated/"
  
  serviceMeshIntegration:
    istio:
      enabled: true
      mTLS: "STRICT"
      retryPolicy: "exponential-backoff"
  
  loadBalancing:
    strategy: "round-robin"
    healthChecking: "enabled"
```

## Resource Components

### Database Resources

**Focus Areas:** Schema management, capacity planning, operational procedures

**Simplified Resource Workflow (Infrastructure, 15-25 minutes):**

1. **catalog-info.yaml:** Mark as kind: Resource
2. **soul.yaml:** Connection details and schemas
3. **index.md:** Operational procedures

**Simplified Resource soul.yaml:**
```yaml
# soul.yaml for resources
connectionMethod: "PostgreSQL on port 5432"
accessControl: "VPC-only access"
backupPolicy: "Daily snapshots, 30-day retention"
schema:
  tables:
    - name: users
      purpose: "Store user credentials"
    - name: sessions
      purpose: "Active user sessions"
```

**Database catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: postgres-payments
  description: PostgreSQL database for payment transaction storage
  labels:
    resource-type: database
    database-engine: postgresql
spec:
  type: database
  lifecycle: production
  owner: team:default/platform-team
  system: payment-processing
```

**Database soul.yaml:**
```yaml
databaseProfile:
  engine: "PostgreSQL"
  version: "14.9"
  
  infrastructure:
    provider: "AWS RDS"
    instanceType: "db.r6g.xlarge"
    storage:
      type: "GP3 SSD"
      size: "1TB"
      iops: 3000
    multiAZ: true
    region: "us-west-2"
    availabilityZones: ["us-west-2a", "us-west-2b"]
  
  connectionProfile:
    endpoint: "payments-db.cluster-xyz.us-west-2.rds.amazonaws.com"
    port: 5432
    database: "payments_production"
    maxConnections: 200
    connectionPooling:
      enabled: true
      tool: "PgBouncer"
      poolSize: 100
    ssl:
      required: true
      version: "TLS 1.3"
  
  schemaProfile:
    tables:
      payments:
        purpose: "Core payment transaction records"
        rowCount: "~5M"
        growthRate: "50K new records/day"
        partitioning: "Monthly by created_at"
        indexes:
          - "idx_payments_user_id"
          - "idx_payments_status"
          - "idx_payments_created_at"
      
      payment_methods:
        purpose: "User payment method storage"
        rowCount: "~500K"
        growthRate: "5K new records/day"
        encryption: "Column-level for sensitive data"
    
    migrations:
      tool: "Flyway"
      location: "migrations/"
      strategy: "Forward-only"
      rollbackStrategy: "Manual intervention required"
  
  operationalProfile:
    backupPolicy:
      frequency: "Continuous (Point-in-time recovery)"
      retention: "35 days"
      crossRegion: "Weekly snapshots to us-east-1"
      testRestores: "Monthly"
    
    maintenanceWindow: "Sunday 2-4 AM PST"
    
    monitoring:
      metrics:
        - "Connection count"
        - "Query performance"
        - "Disk usage"
        - "Replication lag"
      alerting:
        - "Connection pool > 80%"
        - "Slow queries > 5 seconds"
        - "Disk usage > 85%"
    
    capacityPlanning:
      currentUtilization:
        cpu: "45%"
        memory: "60%"
        storage: "40%"
      projectedGrowth: "20% annually"
      scaleUpTriggers:
        - "CPU > 70% for 15 minutes"
        - "Storage > 80%"
```

### Cache Resources

**Focus Areas:** Eviction policies, memory management, cache patterns

**Cache soul.yaml:**
```yaml
cacheProfile:
  engine: "Redis"
  version: "7.0"
  
  infrastructure:
    provider: "AWS ElastiCache"
    nodeType: "cache.r6g.large"
    clusterMode: "enabled"
    shards: 3
    replicasPerShard: 1
  
  memoryManagement:
    maxMemory: "6GB"
    evictionPolicy: "allkeys-lru"
    memoryUsageThreshold: "80%"
  
  cachePatterns:
    - pattern: "Session storage"
      keyPattern: "session:*"
      ttl: "24 hours"
      usage: "High frequency"
    
    - pattern: "API response caching"
      keyPattern: "api:*"
      ttl: "5 minutes"
      usage: "Medium frequency"
    
    - pattern: "User profile caching"
      keyPattern: "user:*"
      ttl: "1 hour"
      usage: "High frequency"
  
  performanceProfile:
    expectedLatency: "< 1ms"
    throughput: "100K ops/second"
    hitRatio: "> 95%"
```

## Website Components

### Single Page Applications

**Focus Areas:** Performance metrics, user experience, accessibility

**SPA catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: customer-portal
  description: Customer-facing web application for account management
  labels:
    website-type: spa
    framework: react
spec:
  type: website
  lifecycle: production
  owner: team:default/frontend-team
  system: customer-experience
  consumesApis:
    - auth-api
    - user-profile-api
    - payment-api
```

**SPA soul.yaml:**
```yaml
webApplicationProfile:
  framework: "React"
  version: "18.2.0"
  buildTool: "Vite"
  
  deploymentProfile:
    hosting: "AWS CloudFront + S3"
    cdn: "CloudFront"
    ssl: "AWS Certificate Manager"
    domains:
      - "portal.company.com"
      - "app.company.com"
  
  performanceProfile:
    coreWebVitals:
      lcp: "< 2.5s"  # Largest Contentful Paint
      fid: "< 100ms"  # First Input Delay
      cls: "< 0.1"    # Cumulative Layout Shift
    
    bundleSize:
      initial: "< 200KB gzipped"
      total: "< 1MB gzipped"
    
    loadingStrategy:
      codesplitting: "Route-based"
      lazyLoading: "Images and non-critical components"
      preloading: "Critical routes"
  
  userExperienceProfile:
    accessibility:
      wcagLevel: "AA"
      screenReaderSupport: true
      keyboardNavigation: true
      colorContrast: "4.5:1 minimum"
    
    browserSupport:
      - "Chrome 90+"
      - "Firefox 88+"
      - "Safari 14+"
      - "Edge 90+"
    
    mobileResponsiveness:
      breakpoints: ["320px", "768px", "1024px", "1440px"]
      touchOptimized: true
      gestureSupport: true
  
  securityProfile:
    contentSecurityPolicy: "strict"
    crossOriginPolicy: "same-origin"
    authenticationFlow: "OAuth 2.0 with PKCE"
    tokenStorage: "httpOnly cookies"
```

### Server-Side Rendered Applications

**Focus Areas:** SEO optimization, server performance, hydration

**SSR soul.yaml:**
```yaml
ssrProfile:
  framework: "Next.js"
  version: "14.0.0"
  renderingStrategy: "Hybrid (SSR + SSG)"
  
  serverProfile:
    runtime: "Node.js 18"
    deployment: "Vercel"
    regions: ["us-west-2", "eu-west-1"]
    
  seoOptimization:
    metaTags: "Dynamic per page"
    structuredData: "JSON-LD"
    sitemap: "Auto-generated"
    robotsTxt: "Custom rules"
  
  hydrationStrategy:
    selective: true
    progressive: true
    criticalCSS: "Inlined"
  
  cachingStrategy:
    staticGeneration: "Build time for marketing pages"
    incrementalRegeneration: "On-demand for dynamic content"
    cdnCaching: "CloudFront with custom headers"
```

## System Components

### Business Domain Systems

**Focus Areas:** Business capability mapping, component relationships, data flow

**System catalog-info.yaml:**
```yaml
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: payment-processing
  description: Complete payment processing capability including transactions, refunds, and reporting
spec:
  owner: team:default/payments-team
  domain: commerce
```

**System soul.yaml:**
```yaml
systemProfile:
  businessCapability: "Payment Processing"
  domain: "Commerce"
  
  componentArchitecture:
    pattern: "Microservices with event-driven communication"
    components:
      - name: "payment-service"
        role: "Core transaction processing"
        criticality: "tier-1"
        type: "service"
      
      - name: "payment-api"
        role: "External interface for payment operations"
        criticality: "tier-1"
        type: "api"
      
      - name: "fraud-detection-service"
        role: "Real-time fraud analysis"
        criticality: "tier-2"
        type: "service"
      
      - name: "payment-reporting-service"
        role: "Analytics and reporting"
        criticality: "tier-3"
        type: "service"
  
  dataFlowProfile:
    primaryFlows:
      - name: "Payment Processing"
        path: "Client → Payment API → Payment Service → Fraud Detection → Database"
        volume: "~10K transactions/day"
        latency: "< 500ms end-to-end"
      
      - name: "Refund Processing"
        path: "Admin Portal → Payment API → Payment Service → External Gateway"
        volume: "~500 refunds/day"
        latency: "< 2 seconds"
    
    eventFlows:
      - name: "Payment Events"
        events: ["payment.created", "payment.completed", "payment.failed"]
        consumers: ["notification-service", "analytics-service", "audit-service"]
  
  businessMetrics:
    kpis:
      - metric: "Payment Success Rate"
        target: "> 99.5%"
        current: "99.7%"
      
      - metric: "Average Processing Time"
        target: "< 300ms"
        current: "245ms"
      
      - metric: "Fraud Detection Rate"
        target: "> 95%"
        current: "97.2%"
  
  complianceProfile:
    standards:
      - "PCI DSS Level 1"
      - "SOX compliance"
      - "GDPR for EU customers"
    
    auditRequirements:
      - "All transactions logged"
      - "Quarterly security assessments"
      - "Annual compliance certification"
```## Sp
ecialized Variations

### Legacy System Documentation

**Focus Areas:** Technical debt documentation, modernization roadmap, risk assessment

**Legacy System Approach:**
```yaml
# catalog-info.yaml for legacy systems
metadata:
  labels:
    legacy-status: active
    modernization-priority: high
    technical-debt-level: high
  annotations:
    legacy.io/original-technology: "COBOL on mainframe"
    legacy.io/modernization-plan: "./modernization-roadmap.md"
```

**Legacy-Specific soul.yaml:**
```yaml
legacyProfile:
  originalTechnology:
    language: "COBOL"
    platform: "IBM z/OS"
    deployedYear: "1987"
    lastMajorUpdate: "2015"
  
  technicalDebt:
    level: "high"
    issues:
      - "No automated testing"
      - "Monolithic architecture"
      - "Outdated dependencies"
      - "Limited documentation"
    
    riskAssessment:
      businessRisk: "high"
      securityRisk: "medium"
      maintenanceRisk: "critical"
      skillsRisk: "high"
  
  modernizationPlan:
    strategy: "Strangler Fig Pattern"
    phases:
      - phase: "API Wrapper"
        duration: "3 months"
        description: "Create REST API facade"
      
      - phase: "Data Migration"
        duration: "6 months"
        description: "Migrate to modern database"
      
      - phase: "Service Extraction"
        duration: "12 months"
        description: "Extract microservices"
    
    targetArchitecture:
      platform: "Kubernetes"
      language: "Java Spring Boot"
      database: "PostgreSQL"
  
  operationalConstraints:
    maintenanceWindow: "Saturday 10 PM - Sunday 6 AM"
    deploymentFrequency: "Monthly"
    rollbackCapability: "Manual process, 4-hour window"
    supportModel: "Limited vendor support"
```

### Third-Party Integration Documentation

**Focus Areas:** External dependencies, SLA management, vendor relationship

**Third-Party Integration soul.yaml:**
```yaml
integrationProfile:
  vendor: "Stripe"
  service: "Payment Processing"
  contractType: "SaaS"
  
  vendorRelationship:
    accountManager: "<EMAIL>"
    technicalContact: "<EMAIL>"
    contractExpiry: "2025-12-31"
    renewalProcess: "Annual review in Q4"
  
  serviceLevel:
    availability: "99.95% (vendor SLA)"
    responseTime: "< 500ms (95th percentile)"
    supportTier: "Premium"
    escalationPath: "Email → Phone → Account Manager"
  
  integrationPattern:
    type: "REST API"
    authentication: "API Key + Webhook signatures"
    dataFlow: "Bidirectional"
    errorHandling: "Retry with exponential backoff"
  
  riskManagement:
    vendorLockIn: "Medium"
    dataPortability: "Export available via API"
    fallbackStrategy: "Queue transactions for retry"
    businessContinuity: "Secondary processor available"
  
  complianceHandling:
    dataProcessing: "Vendor is data processor"
    certifications: ["PCI DSS", "SOC 2"]
    dataResidency: "US and EU regions available"
```

### Experimental Component Documentation

**Focus Areas:** Hypothesis tracking, success metrics, graduation criteria

**Experimental soul.yaml:**
```yaml
experimentProfile:
  hypothesis: "Machine learning-based fraud detection will reduce false positives by 30%"
  startDate: "2024-01-15"
  plannedDuration: "3 months"
  
  successMetrics:
    primary:
      - metric: "False positive rate"
        baseline: "5%"
        target: "< 3.5%"
      
      - metric: "Detection accuracy"
        baseline: "92%"
        target: "> 95%"
    
    secondary:
      - metric: "Processing latency"
        baseline: "200ms"
        target: "< 300ms"
  
  graduationCriteria:
    - "All primary metrics achieved"
    - "No degradation in system stability"
    - "Positive stakeholder feedback"
    - "Cost analysis favorable"
  
  riskMitigation:
    - "Feature flag for instant rollback"
    - "A/B testing with 10% traffic"
    - "Comprehensive monitoring"
    - "Weekly review meetings"
  
  resourceAllocation:
    team: "2 engineers, 1 data scientist"
    budget: "$50K"
    infrastructure: "Dedicated ML training cluster"
```

## Decision Framework

### Choosing the Right Variation

**Decision Tree:**
```
1. What is the primary function?
   ├── Processes requests → Service
   ├── Provides reusable code → Library
   ├── Defines contracts → API
   ├── Manages data/infrastructure → Resource
   ├── Serves users directly → Website
   └── Groups related components → System

2. What is the deployment model?
   ├── Independently deployable → Service
   ├── Embedded in other components → Library
   ├── Contract definition only → API
   ├── Managed infrastructure → Resource
   ├── Static/dynamic web content → Website
   └── Logical grouping → System

3. What are the primary concerns?
   ├── Performance, scaling, reliability → Service
   ├── Reusability, compatibility → Library
   ├── Contract evolution, consumers → API
   ├── Capacity, operations → Resource
   ├── User experience, accessibility → Website
   └── Business alignment, data flow → System
```

**Component Type Selection Matrix:**

| Characteristic | Service | Library | API | Resource | Website | System |
|---------------|---------|---------|-----|----------|---------|--------|
| **Has runtime process** | ✅ | ❌ | ❌ | ✅ | ✅ | N/A |
| **Independently deployable** | ✅ | ❌ | ❌ | ✅ | ✅ | N/A |
| **Serves external requests** | ✅ | ❌ | ✅ | ❌ | ✅ | N/A |
| **Embedded in other code** | ❌ | ✅ | ❌ | ❌ | ❌ | N/A |
| **Defines contracts** | ❌ | ❌ | ✅ | ❌ | ❌ | N/A |
| **Manages infrastructure** | ❌ | ❌ | ❌ | ✅ | ❌ | N/A |
| **User-facing interface** | ❌ | ❌ | ❌ | ❌ | ✅ | N/A |
| **Groups other components** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ |

### Hybrid Approaches

**When to Use Multiple Types:**
1. **Service + API:** When a service provides both implementation and contract
2. **Library + API:** When a library implements a standardized interface
3. **Website + API:** When a web application also provides API endpoints
4. **Resource + API:** When infrastructure exposes programmatic interfaces

**Implementation Strategy:**
```yaml
# Primary component (service)
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: payment-service
spec:
  type: service
  providesApis:
    - payment-api  # Reference to separate API component

---
# Secondary component (API contract)
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: payment-api
spec:
  type: openapi
  definition:
    $text: ./openapi.yaml
```

### Evolution Paths

**Component Lifecycle Evolution:**
```
Experimental → Service → System
     ↓
   Library ← → API
     ↓
  Resource
```

**Common Evolution Patterns:**
1. **Experimental → Service:** Successful experiments become production services
2. **Service → System:** Services grow to encompass multiple related components
3. **Service → Library:** Common functionality extracted into reusable libraries
4. **Service → API:** Service interfaces formalized into contract definitions
5. **Monolith → System:** Large applications decomposed into microservices systems

**Evolution Documentation Strategy:**
```yaml
# Track evolution in soul.yaml
evolutionProfile:
  previousType: "experimental"
  evolutionDate: "2024-01-15"
  evolutionReason: "Graduated after successful 3-month trial"
  
  futureEvolution:
    plannedType: "system"
    timeframe: "Q3 2024"
    trigger: "When we add fraud detection and reporting services"
```

## Time-Saving Tips

### Use Templates
```bash
# Create a template directory
mkdir -p templates/service
cp my-auth-service/* templates/service/

# For new services
cp -r templates/service/* my-new-service/
# Then customize
```

### Start with Generated Content
```bash
# Generate UUID
uuidgen

# Generate current date
date -I

# List API endpoints from code
grep -r "@Route\|@Get\|@Post" src/

# Find dependencies from package.json
jq '.dependencies' package.json
```

## Best Practices Summary

### Universal Best Practices (All Types)
1. **Start with MVP documentation** - Complete basic files first
2. **Use real, measurable values** - Avoid generic descriptions
3. **Maintain cross-references** - Link to related components
4. **Update regularly** - Keep documentation current with changes
5. **Validate continuously** - Use automated checks

### Type-Specific Best Practices

**Services:**
- Focus on operational excellence and monitoring
- Document scaling characteristics and resource requirements
- Include comprehensive error handling and recovery procedures
- Maintain performance benchmarks and SLA definitions

**Libraries:**
- Provide comprehensive function registry with examples
- Document compatibility matrix and version requirements
- Include usage patterns and integration examples
- Maintain clear upgrade and migration guidance

**APIs:**
- Maintain comprehensive OpenAPI specifications
- Document versioning strategy and evolution plans
- Provide consumer guidance and SDK information
- Include rate limiting and authentication details

**Resources:**
- Document operational procedures and maintenance windows
- Include capacity planning and scaling information
- Maintain connection details and access patterns
- Document backup and disaster recovery procedures

**Websites:**
- Focus on user experience and performance metrics
- Document accessibility compliance and browser support
- Include deployment and hosting information
- Maintain SEO and analytics configuration

**Systems:**
- Document business capabilities and value streams
- Include component relationships and data flows
- Maintain business metrics and KPI definitions
- Document compliance and governance requirements

---

*Choose the variation that best matches your component's primary function and concerns. Remember that components can evolve over time, and documentation should reflect both current state and planned evolution.*