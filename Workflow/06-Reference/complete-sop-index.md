## Table of Contents

- [Complete SOP Index](#complete-sop-index)
  - [Introduction](#introduction)
  - [Master Procedure Overview](#master-procedure-overview)
    - [The Cortex App Generation Workflow](#the-cortex-app-generation-workflow)
  - [Phase-Based Procedure Index](#phase-based-procedure-index)
    - [Phase 1: Strategic Planning & Gap Analysis](#phase-1-strategic-planning-gap-analysis)
      - [1.1 Epic Decomposition Process](#11-epic-decomposition-process)
      - [1.2 Component Discovery Process](#12-component-discovery-process)
      - [1.3 Formal Gap Analysis Process](#13-formal-gap-analysis-process)
    - [Phase 2: Component Architecture & Blueprinting](#phase-2-component-architecture-blueprinting)
      - [2.1 Component Specification Design](#21-component-specification-design)
      - [2.2 Draft Component Manifest Creation](#22-draft-component-manifest-creation)
      - [2.3 Architectural Block Assembly](#23-architectural-block-assembly)
      - [2.4 Code Mapping Preparation](#24-code-mapping-preparation)
    - [Phase 3: Implementation & Automated Governance](#phase-3-implementation-automated-governance)
      - [3.1 Soul Forge Scaffolding](#31-soul-forge-scaffolding)
      - [3.2 Business Logic Implementation](#32-business-logic-implementation)
      - [3.3 Architectural Integrity Enforcement](#33-architectural-integrity-enforcement)
      - [3.4 Automated Documentation Generation](#34-automated-documentation-generation)
    - [Phase 4: Finalization, Review & Integration](#phase-4-finalization-review-integration)
      - [4.1 Elevated Human Review Process](#41-elevated-human-review-process)
      - [4.2 Component Manifest Finalization](#42-component-manifest-finalization)
      - [4.3 Deployment and Integration](#43-deployment-and-integration)
      - [4.4 System Integration Verification](#44-system-integration-verification)
  - [Cross-Cutting Procedures](#cross-cutting-procedures)
    - [Quality Assurance Framework](#quality-assurance-framework)
    - [Documentation Lifecycle Management](#documentation-lifecycle-management)
    - [Code Mapping and Traceability](#code-mapping-and-traceability)
  - [Dependency Mapping](#dependency-mapping)
    - [Sequential Dependencies](#sequential-dependencies)
    - [Parallel Opportunities](#parallel-opportunities)
    - [Critical Path Items](#critical-path-items)
  - [Quick Reference Lookup](#quick-reference-lookup)
    - [Procedure Lookup Table](#procedure-lookup-table)
    - [By Phase](#by-phase)
    - [By Activity Type](#by-activity-type)
    - [By Automation Level](#by-automation-level)
    - [By Stakeholder](#by-stakeholder)
  - [Supporting Materials Reference](#supporting-materials-reference)
    - [Templates and Examples](#templates-and-examples)
    - [Automation and Tooling](#automation-and-tooling)
    - [Implementation Guidance](#implementation-guidance)
    - [Foundational Understanding](#foundational-understanding)
    - [Reference Materials](#reference-materials)
  - [Appendix: Key Concepts from Original SOP](#appendix-key-concepts-from-original-sop)
    - [The "Soul Forge" Philosophy](#the-soul-forge-philosophy)
    - [The App Generator Vision](#the-app-generator-vision)

---

# Complete SOP Index

## Introduction

This comprehensive Standard Operating Procedure (SOP) index provides a master reference to all procedures, processes, and methodologies within the Cortex workflow generator system. It serves as the authoritative guide for navigating the complete methodology, with cross-references to the new semantic structure and dependency mapping for systematic execution.

## Master Procedure Overview

### The Cortex App Generation Workflow
**Primary Objective**: Transform feature requests into fully integrated, documented, and validated system components through systematic planning, design, implementation, and deployment.

**Core Philosophy**: Planning-first development that creates "AI Context Heaven" through comprehensive specification before implementation, enabling automated execution and quality assurance.

## Phase-Based Procedure Index

### Phase 1: Strategic Planning & Gap Analysis

**Objective**: Analyze feature requests, discover existing capabilities, and identify architectural gaps before design work begins.

#### 1.1 Epic Decomposition Process
**Location**: [Planning and Discovery](../02-Process/planning-and-discovery.md#epic-decomposition)
**Purpose**: Translate high-level feature requests into concrete technical capabilities
**Prerequisites**: Feature request or user story
**Outputs**: Structured capability requirements list
**Dependencies**: None (entry point)

**Detailed Steps**:
1. **Capability Extraction**: Break down user stories into technical requirements
2. **Scope Definition**: Establish feature boundaries and success criteria
3. **Stakeholder Alignment**: Confirm understanding with business stakeholders
4. **Technical Translation**: Convert business needs into system capabilities

**Quality Gates**:
- All capabilities are measurable and testable
- Business value is clearly articulated
- Technical feasibility is confirmed

#### 1.2 Component Discovery Process
**Location**: [Planning and Discovery](../02-Process/planning-and-discovery.md#component-discovery)
**Purpose**: Systematically search and inventory existing system components
**Prerequisites**: Capability requirements from Epic Decomposition
**Outputs**: Comprehensive inventory of existing components
**Dependencies**: [Epic Decomposition](#11-epic-decomposition-process)

**Detailed Steps**:
1. **Catalog Querying**: Search Backstage component catalog systematically
2. **Code Analysis**: Static analysis of existing codebases
3. **API Discovery**: Identification of existing APIs and capabilities
4. **Relationship Mapping**: Understanding component interdependencies

**Quality Gates**:
- All relevant existing components identified
- Component capabilities accurately assessed
- Relationships and dependencies documented

#### 1.3 Formal Gap Analysis Process
**Location**: [Planning and Discovery](../02-Process/planning-and-discovery.md#gap-analysis)
**Purpose**: Compare required capabilities against existing components to identify gaps
**Prerequisites**: Capability requirements and component inventory
**Outputs**: Definitive list of components to reuse, refactor, or create new
**Dependencies**: [Epic Decomposition](#11-epic-decomposition-process), [Component Discovery](#12-component-discovery-process)

**Detailed Steps**:
1. **Capability Matching**: Map existing components to required capabilities
2. **Reuse Assessment**: Evaluate existing components for direct reuse
3. **Refactor Analysis**: Assess components that need modification
4. **Gap Identification**: Identify capabilities requiring new components
5. **Scoring and Prioritization**: Quantitative assessment using scoring rubrics

**Quality Gates**:
- All capabilities have identified solutions (reuse/refactor/new)
- Scoring rubrics applied consistently
- Development effort estimated
- Risk assessment completed

**Reference Materials**:
- [Component Templates](../05-Templates/component-templates.md) - Epic assembly and scoring rubrics
- [Workflow Examples](../05-Templates/workflow-examples.md) - Gap analysis examples

### Phase 2: Component Architecture & Blueprinting

**Objective**: Design detailed specifications for new components and create formal pre-code blueprints within the Backstage catalog.

#### 2.1 Component Specification Design
**Location**: [Architecture and Design](../02-Process/architecture-and-design.md#component-specification)
**Purpose**: Create detailed technical specifications for all new components
**Prerequisites**: Gap analysis results identifying new components needed
**Outputs**: Complete component specifications with rationale, parameters, and testing scenarios
**Dependencies**: [Formal Gap Analysis](#13-formal-gap-analysis-process)

**Detailed Steps**:
1. **Functional Requirements**: Define what the component must do
2. **Interface Design**: Specify APIs, events, and data contracts
3. **Internal Architecture**: Design component internal structure
4. **Business Rules**: Define logic and constraints
5. **Testing Strategy**: Plan validation and verification approaches
6. **Operational Requirements**: Specify monitoring, logging, and maintenance needs

**Quality Gates**:
- All functional requirements are testable
- Interfaces are well-defined and documented
- Business rules are complete and unambiguous
- Testing strategy covers all scenarios

#### 2.2 Draft Component Manifest Creation
**Location**: [Architecture and Design](../02-Process/architecture-and-design.md#component-manifests)
**Purpose**: Create Backstage catalog entries for all new components in DRAFT state
**Prerequisites**: Component specifications
**Outputs**: `catalog-info.yaml` files for all new components
**Dependencies**: [Component Specification Design](#21-component-specification-design)

**Detailed Steps**:
1. **Metadata Definition**: Name, description, type, lifecycle status
2. **Ownership Assignment**: Team or individual responsibility
3. **Relationship Mapping**: Dependencies and provides relationships
4. **System Integration**: Parent system/domain assignment
5. **Tag and Annotation**: Enhanced discoverability metadata

**Quality Gates**:
- All required metadata fields completed
- Ownership clearly assigned
- Relationships accurately defined
- Naming conventions followed

#### 2.3 Architectural Block Assembly
**Location**: [Architecture and Design](../02-Process/architecture-and-design.md#architectural-relationships)
**Purpose**: Define component relationships to build feature's conceptual graph
**Prerequisites**: Draft component manifests
**Outputs**: Complete relationship graph for the feature
**Dependencies**: [Draft Component Manifest Creation](#22-draft-component-manifest-creation)

**Detailed Steps**:
1. **Dependency Definition**: Specify `dependsOn` relationships
2. **API Provision**: Define `providesApi` relationships
3. **Event Relationships**: Map event producers and consumers
4. **Data Flow Mapping**: Document data movement between components
5. **Integration Validation**: Verify relationship consistency

**Quality Gates**:
- All relationships are bidirectional where appropriate
- No circular dependencies exist
- Integration points are well-defined
- Data flow is logical and efficient

#### 2.4 Code Mapping Preparation
**Location**: [Architecture and Design](../02-Process/architecture-and-design.md#code-mapping)
**Purpose**: Prepare code mapping annotations and initial code beacon files
**Prerequisites**: Component specifications and manifests
**Outputs**: Initial `.codebeacons.json` files and repository location plans
**Dependencies**: [Architectural Block Assembly](#23-architectural-block-assembly)

**Detailed Steps**:
1. **Repository Planning**: Define source code location strategy
2. **Symbol Identification**: List primary symbols to be implemented
3. **Code Beacon Creation**: Create initial `.codebeacons.json` files
4. **Mapping Validation**: Verify mapping completeness and accuracy

**Quality Gates**:
- All components have planned repository locations
- Primary symbols are identified and documented
- Code beacon files are syntactically correct
- Mapping strategy is consistent across components

**Reference Materials**:
- [Code Mapping and Traceability](../04-Automation/code-mapping-and-traceability.md) - Technical implementation details
- [Enhanced Soul Forge Template](../05-Templates/enhanced-soul-forge-template.md) - Template integration

### Phase 3: Implementation & Automated Governance

**Objective**: Write high-quality code that adheres to architectural blueprints with continuous automated validation.

#### 3.1 Soul Forge Scaffolding
**Location**: [Implementation and Validation](../02-Process/implementation-and-validation.md#scaffolding)
**Purpose**: Generate component repositories using Enhanced Soul Forge Template
**Prerequisites**: Component specifications and code mapping plans
**Outputs**: Scaffolded repositories with boilerplate code and configuration
**Dependencies**: [Code Mapping Preparation](#24-code-mapping-preparation)

**Detailed Steps**:
1. **Template Selection**: Choose appropriate Soul Forge template variant
2. **Repository Generation**: Create repository structure and boilerplate
3. **Configuration Setup**: Initialize CI/CD, validation hooks, and documentation
4. **Integration Preparation**: Set up connections to existing systems
5. **Quality Framework**: Initialize testing and validation infrastructure

**Quality Gates**:
- Repository structure follows established conventions
- All required boilerplate is present and correct
- CI/CD pipeline is functional
- Documentation framework is initialized

#### 3.2 Business Logic Implementation
**Location**: [Implementation and Validation](../02-Process/implementation-and-validation.md#implementation)
**Purpose**: Write core application code following component specifications
**Prerequisites**: Scaffolded repositories and detailed specifications
**Outputs**: Functional component implementations
**Dependencies**: [Soul Forge Scaffolding](#31-soul-forge-scaffolding)

**Detailed Steps**:
1. **Core Logic Development**: Implement primary component functionality
2. **Interface Implementation**: Code APIs, events, and data contracts
3. **Business Rule Enforcement**: Implement logic and constraints
4. **Error Handling**: Add comprehensive error handling and recovery
5. **Performance Optimization**: Ensure implementation meets performance requirements

**Quality Gates**:
- All functional requirements are implemented
- Interfaces match specifications exactly
- Business rules are correctly enforced
- Error handling is comprehensive
- Performance requirements are met

#### 3.3 Architectural Integrity Enforcement
**Location**: [Implementation and Validation](../02-Process/implementation-and-validation.md#validation-hooks)
**Purpose**: Automated validation of implementation against architectural specifications
**Prerequisites**: Implemented code and validation hook configuration
**Outputs**: Validated, architecturally compliant code
**Dependencies**: [Business Logic Implementation](#32-business-logic-implementation)

**Detailed Steps**:
1. **Dependency Validation**: Static analysis of import dependencies
2. **Code Beacon Verification**: Validation of code mapping accuracy
3. **Interface Compliance**: Verification of API contract adherence
4. **Architectural Pattern**: Validation of design pattern compliance
5. **Quality Metrics**: Assessment of code quality and maintainability

**Quality Gates**:
- All dependencies are declared in manifests
- Code beacons point to valid symbols
- Interfaces match specifications
- Architectural patterns are followed
- Quality metrics meet standards

#### 3.4 Automated Documentation Generation
**Location**: [Implementation and Validation](../02-Process/implementation-and-validation.md#documentation-generation)
**Purpose**: Extract technical details from code and integrate with human-authored documentation
**Prerequisites**: Implemented code and documentation framework
**Outputs**: Complete, synchronized documentation
**Dependencies**: [Architectural Integrity Enforcement](#33-architectural-integrity-enforcement)

**Detailed Steps**:
1. **API Schema Extraction**: Generate API documentation from specifications
2. **Database Schema Documentation**: Extract database structure documentation
3. **Type Documentation**: Generate interface documentation from code types
4. **Example Integration**: Include code examples from test files
5. **Documentation Synchronization**: Ensure documentation matches implementation

**Quality Gates**:
- All APIs are documented with current schemas
- Database documentation is complete and accurate
- Type documentation covers all public interfaces
- Examples are functional and current
- Documentation is synchronized with code

**Reference Materials**:
- [Validation Framework](../04-Automation/validation-framework.md) - Technical validation details
- [Documentation Generation](../04-Automation/documentation-generation.md) - Automation implementation

### Phase 4: Finalization, Review & Integration

**Objective**: Graduate features from draft to fully integrated, discoverable ecosystem components.

#### 4.1 Elevated Human Review Process
**Location**: [Integration and Deployment](../02-Process/integration-and-deployment.md#human-review)
**Purpose**: Conduct focused human review with CI guaranteeing architectural compliance
**Prerequisites**: Implemented, validated, and documented components
**Outputs**: Approved components ready for finalization
**Dependencies**: [Automated Documentation Generation](#34-automated-documentation-generation)

**Detailed Steps**:
1. **Logic Review**: Assess business logic correctness and efficiency
2. **Quality Assessment**: Evaluate code quality and maintainability
3. **Acceptance Criteria Verification**: Confirm implementation meets original requirements
4. **Integration Testing**: Validate component interactions
5. **Performance Validation**: Confirm operational requirements are met

**Quality Gates**:
- Business logic is correct and efficient
- Code quality meets organizational standards
- All acceptance criteria are satisfied
- Integration tests pass
- Performance requirements are met

#### 4.2 Component Manifest Finalization
**Location**: [Integration and Deployment](../02-Process/integration-and-deployment.md#manifest-finalization)
**Purpose**: Update component manifests from DRAFT to FINAL status
**Prerequisites**: Approved component implementations
**Outputs**: Finalized component manifests ready for catalog integration
**Dependencies**: [Elevated Human Review Process](#41-elevated-human-review-process)

**Detailed Steps**:
1. **Status Update**: Change lifecycle status from DRAFT to FINAL
2. **Metadata Verification**: Confirm all metadata is accurate and complete
3. **Relationship Validation**: Verify all relationships are correctly defined
4. **Documentation Links**: Ensure all documentation references are valid
5. **Catalog Integration**: Prepare for Backstage catalog publication

**Quality Gates**:
- All metadata is accurate and complete
- Relationships are correctly defined and validated
- Documentation links are functional
- Catalog integration requirements are met

#### 4.3 Deployment and Integration
**Location**: [Integration and Deployment](../02-Process/integration-and-deployment.md#deployment)
**Purpose**: Deploy components and integrate with production systems
**Prerequisites**: Finalized component manifests and approved implementations
**Outputs**: Deployed, operational components
**Dependencies**: [Component Manifest Finalization](#42-component-manifest-finalization)

**Detailed Steps**:
1. **Deployment Pipeline Execution**: Trigger automated deployment processes
2. **Environment Configuration**: Configure production environment settings
3. **Integration Validation**: Verify connections to existing systems
4. **Monitoring Setup**: Initialize operational monitoring and alerting
5. **Rollback Preparation**: Ensure rollback procedures are ready

**Quality Gates**:
- Deployment completes successfully
- All integrations are functional
- Monitoring is operational
- Rollback procedures are tested and ready

#### 4.4 System Integration Verification
**Location**: [Integration and Deployment](../02-Process/integration-and-deployment.md#verification)
**Purpose**: Confirm components appear correctly in Backstage catalog with proper relationships
**Prerequisites**: Deployed components and finalized manifests
**Outputs**: Verified, discoverable components in production catalog
**Dependencies**: [Deployment and Integration](#43-deployment-and-integration)

**Detailed Steps**:
1. **Catalog Verification**: Confirm components appear in Backstage catalog
2. **Relationship Validation**: Verify all relationships are correctly displayed
3. **Documentation Integration**: Confirm documentation is accessible and current
4. **Search Functionality**: Verify components are discoverable through search
5. **User Acceptance**: Confirm components meet user needs and expectations

**Quality Gates**:
- Components are visible in Backstage catalog
- All relationships are correctly displayed
- Documentation is accessible and current
- Components are discoverable through search
- User acceptance criteria are met

## Cross-Cutting Procedures

### Quality Assurance Framework
**Location**: [Validation Framework](../04-Automation/validation-framework.md)
**Purpose**: Comprehensive quality validation across all phases
**Application**: Continuous throughout all phases

**Key Components**:
- **Pre-commit Validation**: Local quality checks before code submission
- **CI/CD Integration**: Automated validation in build pipelines
- **Quality Metrics**: Continuous monitoring of quality indicators
- **Compliance Checking**: Automated verification of standards adherence

### Documentation Lifecycle Management
**Location**: [Documentation Generation](../04-Automation/documentation-generation.md)
**Purpose**: Maintain documentation accuracy and completeness
**Application**: Continuous throughout implementation and maintenance

**Key Components**:
- **Automated Generation**: Extract documentation from code
- **Synchronization Validation**: Ensure documentation matches implementation
- **Version Management**: Track documentation changes with code changes
- **Quality Assessment**: Validate documentation completeness and accuracy

### Code Mapping and Traceability
**Location**: [Code Mapping and Traceability](../04-Automation/code-mapping-and-traceability.md)
**Purpose**: Maintain bidirectional links between documentation and code
**Application**: Established in Phase 2, maintained throughout lifecycle

**Key Components**:
- **Mapping Creation**: Establish initial code-documentation links
- **Drift Detection**: Automated identification of mapping inconsistencies
- **Validation Integration**: Continuous verification of mapping accuracy
- **Maintenance Procedures**: Processes for updating mappings as code evolves

## Dependency Mapping

### Sequential Dependencies
```
Epic Decomposition → Component Discovery → Gap Analysis
Gap Analysis → Component Specification → Draft Manifests
Draft Manifests → Block Assembly → Code Mapping
Code Mapping → Scaffolding → Implementation
Implementation → Validation → Documentation Generation
Documentation Generation → Human Review → Finalization
Finalization → Deployment → Verification
```

### Parallel Opportunities
- **Component Specification** and **Draft Manifest Creation** can be done in parallel for different components
- **Business Logic Implementation** can proceed in parallel across multiple components
- **Documentation Generation** can run in parallel with **Validation Hooks**

### Critical Path Items
1. **Gap Analysis** - Blocks all subsequent design work
2. **Component Specification** - Required for implementation
3. **Architectural Integrity Enforcement** - Gates deployment
4. **Human Review** - Required for finalization

## Quick Reference Lookup

### Procedure Lookup Table

| Procedure | Phase | Location | Prerequisites | Duration |
|-----------|-------|----------|---------------|----------|
| Epic Decomposition | 1 | [Planning and Discovery](../02-Process/planning-and-discovery.md#epic-decomposition) | Feature request | 1-2 hours |
| Component Discovery | 1 | [Planning and Discovery](../02-Process/planning-and-discovery.md#component-discovery) | Capability requirements | 2-4 hours |
| Gap Analysis | 1 | [Planning and Discovery](../02-Process/planning-and-discovery.md#gap-analysis) | Component inventory | 1-3 hours |
| Component Specification | 2 | [Architecture and Design](../02-Process/architecture-and-design.md#component-specification) | Gap analysis results | 2-6 hours |
| Draft Manifests | 2 | [Architecture and Design](../02-Process/architecture-and-design.md#component-manifests) | Component specs | 1-2 hours |
| Block Assembly | 2 | [Architecture and Design](../02-Process/architecture-and-design.md#architectural-relationships) | Draft manifests | 1-2 hours |
| Code Mapping | 2 | [Architecture and Design](../02-Process/architecture-and-design.md#code-mapping) | Block assembly | 30-60 min |
| Scaffolding | 3 | [Implementation and Validation](../02-Process/implementation-and-validation.md#scaffolding) | Code mapping | 15-30 min |
| Implementation | 3 | [Implementation and Validation](../02-Process/implementation-and-validation.md#implementation) | Scaffolded repos | Variable |
| Validation | 3 | [Implementation and Validation](../02-Process/implementation-and-validation.md#validation-hooks) | Implementation | Automated |
| Documentation Gen | 3 | [Implementation and Validation](../02-Process/implementation-and-validation.md#documentation-generation) | Implementation | Automated |
| Human Review | 4 | [Integration and Deployment](../02-Process/integration-and-deployment.md#human-review) | Validated code | 1-4 hours |
| Finalization | 4 | [Integration and Deployment](../02-Process/integration-and-deployment.md#manifest-finalization) | Approved code | 15-30 min |
| Deployment | 4 | [Integration and Deployment](../02-Process/integration-and-deployment.md#deployment) | Finalized manifests | Automated |
| Verification | 4 | [Integration and Deployment](../02-Process/integration-and-deployment.md#verification) | Deployment | 15-30 min |

### By Phase
- **Phase 1**: [Planning and Discovery](../02-Process/planning-and-discovery.md)
- **Phase 2**: [Architecture and Design](../02-Process/architecture-and-design.md)
- **Phase 3**: [Implementation and Validation](../02-Process/implementation-and-validation.md)
- **Phase 4**: [Integration and Deployment](../02-Process/integration-and-deployment.md)

### By Activity Type
- **Planning**: Epic Decomposition, Component Discovery, Gap Analysis
- **Design**: Component Specification, Manifest Creation, Block Assembly
- **Implementation**: Scaffolding, Business Logic, Validation, Documentation
- **Integration**: Review, Finalization, Deployment, Verification

### By Automation Level
- **Manual**: Epic Decomposition, Component Specification, Human Review
- **Semi-Automated**: Component Discovery, Gap Analysis, Block Assembly
- **Fully Automated**: Scaffolding, Validation, Documentation Generation, Deployment

### By Stakeholder
- **Business**: Epic Decomposition, Human Review, Verification
- **Architecture**: Component Specification, Block Assembly, Code Mapping
- **Development**: Implementation, Validation, Documentation
- **Operations**: Deployment, Integration, Monitoring

## Supporting Materials Reference

### Templates and Examples
- [Component Templates](../05-Templates/component-templates.md) - Reusable patterns and scoring rubrics
- [Workflow Examples](../05-Templates/workflow-examples.md) - Complete implementation walkthroughs
- [Enhanced Soul Forge Template](../05-Templates/enhanced-soul-forge-template.md) - Advanced scaffolding template

### Automation and Tooling
- [Validation Framework](../04-Automation/validation-framework.md) - Automated quality assurance
- [Documentation Generation](../04-Automation/documentation-generation.md) - Automated documentation extraction
- [Code Mapping and Traceability](../04-Automation/code-mapping-and-traceability.md) - Bidirectional code-documentation linking

### Implementation Guidance
- [Quick Start Guide](../03-Implementation/quick-start-guide.md) - Rapid implementation workflow
- [Repository Setup](../03-Implementation/repository-setup.md) - File organization and structure
- [Workflow Variations](../03-Implementation/workflow-variations.md) - Specialized approaches for different component types

### Foundational Understanding
- [Philosophy and Vision](../01-Foundation/philosophy-and-vision.md) - Core principles, "Soul Forge" philosophy, and App Generator vision
- [Methodology Overview](../01-Foundation/methodology-overview.md) - Complete process understanding and planning-first development
- [Getting Started](../01-Foundation/getting-started.md) - Entry points and learning paths

### Reference Materials
- [Glossary and Concepts](../06-Reference/glossary-and-concepts.md) - Comprehensive terminology definitions including Soul Forge, Code Beacons, Crown Jewel, and Cortex concepts
- [Navigation Guide](../06-Reference/navigation-guide.md) - User journey mapping and wayfinding assistance

## Appendix: Key Concepts from Original SOP

### The "Soul Forge" Philosophy
The guiding metaphor of the Soul Forge represents a perfect, navigable model of the codebase that enables "AI Context Heaven" - a state where comprehensive specification before implementation allows for automated execution and quality assurance.

### The App Generator Vision
The ultimate vision transforms this manual SOP into a fully automated, AI-driven tool that:
- **Accelerates Phase 1** through AI-powered search and gap analysis
- **Streamlines Phase 2** via conversational component specification
- **Augments Phase 3** with AI coding partners grounded by architectural blueprints
- **Optimizes Phase 4** through automated PR preparation and manifest finalization

This vision turns quality assurance into unprecedented development velocity by handling process "bureaucracy" at machine speed.

---

*This SOP index serves as the master reference for all Cortex methodology procedures. For detailed implementation guidance, refer to the specific process documents linked throughout this index.*