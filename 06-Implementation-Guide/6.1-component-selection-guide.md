# Component Selection Guide

*This guide is planned for development.*

## Planned Content

### Decision Trees
- Is it runnable? → Service
- Is it a contract? → API
- Is it reusable code? → Library
- Is it infrastructure? → Resource
- Is it user-facing? → Website

### Common Patterns
- Microservice architectures
- API-first development
- Shared library strategies
- Infrastructure as code

### Anti-Patterns
- Wrong component types
- Over-engineering
- Under-documentation
- Coupling violations

## Temporary Reference

Until this guide is complete, refer to:
- [Component Templates README](../04-Component-Templates/README.md) for component type overview
- [Best Practices](6.3-best-practices.md) for selection guidance
