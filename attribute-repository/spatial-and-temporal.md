# Spatial and Temporal Dimension Attributes

This file contains attributes related to architecture, integration, structure, history, and future plans.

## Spatial Dimension Sub-Groups

### Architectural Position
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| system | Parent system. | string; Example: "User Management Platform" | soul.yaml | - |
| domain | Business domain. | string; Example: "Identity and Access" | soul.yaml/catalog-info.yaml | - |
| layer | Architectural layer. | string; Example: "Business Logic Layer" | soul.yaml | - |
| pattern | Design pattern. | string; Example: "Microservice (Domain-Driven Design)" | soul.yaml | - |
| style | Architectural style. | string; Example: "Event-driven with CQRS" | soul.yaml | - |
| boundaries | Upstream/peers/downstream. | object; Example: {upstream: ["API Gateway"]} | soul.yaml | - |
| microserviceBoundary | Service boundaries. | string; Example: "Bounded context: User Auth" | Future Suggestion | DDD scaling |

### Data Flow
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| inputs | Input details. | array/object; Example: [{source: "API Gateway", protocol: "HTTPS"}] | soul.yaml | - |
| processing | Processing steps. | array; Example: [{step: "Request Validation", location: "src/validators/"}] | soul.yaml | - |
| outputs | Output details. | array; Example: [{destination: "HTTP Response", format: "JSON"}] | soul.yaml | - |
| sideEffects | Additional effects. | array; Example: [{effect: "Cache Invalidation", target: "Redis"}] | soul.yaml | - |
| dataLineage | Data flow tracing. | object; Example: {from: "User Input", to: "Database"} | Future Suggestion | Compliance auditing |

### Data Model
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| entities | Data entities. | array/object; Example: [{name: "User", table: "users"}] | soul.yaml | - |
| relationships | Entity relations. | array; Example: [{type: "one-to-many", from: "User", to: "Session"}] | soul.yaml | - |
| dataRetention | Retention policies. | object; Example: {default: "7 years"} | soul.yaml | - |
| dataSensitivity | Sensitivity levels. | string; Example: "PII" | Future Suggestion | Privacy compliance |

## Temporal Dimension Sub-Groups

### Genesis
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| problem | Initial problem solved. | string; Example: "Manual user provisioning" | soul.yaml | - |
| businessCase | Justification. | object; Example: {cost: "150 hours/month"} | soul.yaml | - |
| alternativesConsidered | Options evaluated. | array; Example: [{option: "Purchase Okta", rejected: true}] | soul.yaml | - |
| initialConstraints | Starting limits. | object; Example: {timeline: "6 weeks"} | soul.yaml | - |
| originMetrics | Initial KPIs. | object; Example: {initialUsers: 1000} | Future Suggestion | Growth tracking |

### Evolution Path
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| milestones | Key updates. | array; Example: [{version: "1.0.0", date: "2023-03-01"}] | soul.yaml | - |
| technicalDebtHistory | Debt records. | array; Example: [{date: "2023-09-01", debt: "Singleton pattern"}] | soul.yaml | - |
| upgradeHistory | Version upgrades. | array; Example: [{from: "v1", to: "v2", date: "2024-01-01"}] | Future Suggestion | Dependency management |

### Future Direction
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| nextQuarter | Short-term plans. | object; Example: {version: "2.5.0", initiatives: [{feature: "GraphQL API"}]} | soul.yaml | - |
| nextYear | Long-term plans. | object; Example: {version: "3.0.0", initiatives: [{feature: "Serverless transformation"}]} | soul.yaml | - |
| deprecationPlan | Sunset plans. | array; Example: [{component: "v1 REST API", sunset: "2024-Q4"}] | soul.yaml | - |
| scalingRoadmap | Growth plans. | object; Example: {targetUsers: 1000000, timeline: "2025"} | Future Suggestion | Enterprise scaling |
