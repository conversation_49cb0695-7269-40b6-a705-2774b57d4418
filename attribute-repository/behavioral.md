# Behavioral Dimension Attributes

This file contains attributes related to functionality, performance, and testing.

## Behavioral Dimension Sub-Groups

### Functional DNA
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| coreCapabilities | Main functions. | array/object; Example: [{capability: "User Authentication", operations: [{name: "login"}]}] | soul.yaml | - |
| algorithms | Key algorithms. | array; Example: [{name: "Password Hashing", implementation: "PBKDF2"}] | soul.yaml | - |
| quickWins | Easy improvements. | array; Example: [{feature: "Bulk user import", impact: "Saves 10 hours/week"}] | soul.yaml | - |
| limitations | Known constraints. | array; Example: [{constraint: "Max 1000 users per bulk operation"}] | soul.yaml | - |
| businessRules | Rules enforced. | array; Example: ["Max 3 failed attempts before lockout"] | soul.yaml/index.md | - |

### Usage Examples
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| commonPatterns | Typical usage. | array; Example: [{pattern: "Standard Authentication Flow", code: "// code snippet"}] | soul.yaml | - |
| configurations | Environment configs. | array; Example: [{environment: "development", settings: "AUTH_TIMEOUT: 3600"}] | soul.yaml | - |
| usageGuidelines | How-to guides. | string/markdown; Example: "Always validate inputs before processing" | index.md | - |

### Testing Scenarios
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| happyPath | Successful scenarios. | array; Example: [{scenario: "Successful Login Flow", steps: ["User submits valid credentials"]}] | soul.yaml | - |
| errorCases | Failure scenarios. | array; Example: [{scenario: "Invalid Credentials", trigger: "Wrong password submitted"}] | soul.yaml | - |
| edgeCases | Boundary scenarios. | array; Example: [{scenario: "Concurrent Login Attempts", handling: "All succeed"}] | soul.yaml | - |
| loadTests | Performance tests. | array; Example: [{scenario: "Peak Load", parameters: {concurrent_users: 1000}}] | soul.yaml | - |

### Performance Characteristics
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| latency | Response times. | object; Example: {p50: "25ms", p95: "100ms"} | soul.yaml | - |
| throughput | Processing capacity. | object; Example: {sustained: "500 req/s"} | soul.yaml | - |
| resourceUsage | Resource consumption. | object; Example: {cpu: {idle: "100m"}} | soul.yaml | - |
| bottlenecks | Performance limits. | array; Example: [{component: "Database connection pool", limit: "100 connections"}] | soul.yaml | - |
| scalingBehavior | How it scales. | string; Example: "Horizontal via Kubernetes HPA" | index.md | - |
