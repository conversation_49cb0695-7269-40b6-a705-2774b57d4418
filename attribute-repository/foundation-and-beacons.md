# Foundation Layer and Code Beacons Attributes

This file contains attributes related to basic identity, governance, operations, and code navigation aids.

## Foundation Layer Sub-Groups

### Identity Basics
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| name | Unique identifier for the entity. | string; Example: "auth-service" | catalog-info.yaml | - |
| title | Human-readable display name. | string; Example: "Authentication Service" | catalog-info.yaml | - |
| description | Brief overview of purpose. | string; Example: "Handles user authentication" | all files | - |
| uuid | Globally unique identifier. | string; Example: "550e8400-e29b-41d4-a716-************" | soul.yaml | - |
| kind | High-level entity category. | string/enum; Example: "Component" | catalog-info.yaml | - |
| type | Specific subtype. | string/enum; Example: "service" | catalog-info.yaml/soul.yaml | - |
| version | Current version of the entity. | string; Example: "2.3.1" | soul.yaml | - |
| created | Creation timestamp. | string/ISO8601; Example: "2024-01-01T00:00:00Z" | soul.yaml | - |
| lastModified | Last update timestamp. | string/ISO8601; Example: "2024-03-15T10:30:00Z" | soul.yaml | - |
| status | Current operational status. | string/enum; Example: "healthy" | Future Suggestion | Real-time monitoring integration |

### Governance
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| owner | Primary maintainer. | string; Example: "team:platform-team" | catalog-info.yaml/soul.yaml | - |
| stakeholders | Involved parties. | object/array; Example: {executive: ["CTO"], business: ["Product"]} | soul.yaml | - |
| lifecycle | Development stage. | string/enum; Example: "production" | catalog-info.yaml/soul.yaml | - |
| compliance | Standards met. | array; Example: ["SOC2", "GDPR"] | soul.yaml | - |
| sla | Service level agreement. | object; Example: {availability: "99.99%"} | soul.yaml | - |
| auditFrequency | How often compliance is checked. | string; Example: "quarterly" | Future Suggestion | Enterprise reporting |

### Operations
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| platform | Hosting environment. | string; Example: "Kubernetes on AWS EKS" | soul.yaml | - |
| deploymentStrategy | How updates are rolled out. | string; Example: "Blue-Green" | soul.yaml | - |
| deploymentFrequency | Update cadence. | string; Example: "Weekly" | soul.yaml | - |
| monitoringTools | Tools used. | array; Example: ["Prometheus", "Grafana"] | soul.yaml | - |
| dashboards | Monitoring URLs. | array; Example: ["https://grafana/d/abc123"] | soul.yaml | - |
| incidentsManagement | Incident tool. | string; Example: "PagerDuty" | soul.yaml | - |
| runbook | Operational guide URL. | string; Example: "https://runbook.example.com" | soul.yaml | - |
| logging | Logging config. | object; Example: {strategy: "Structured JSON", level: "INFO"} | soul.yaml | - |
| autoScalingRules | Scaling triggers. | object; Example: {cpuThreshold: "70%"} | Future Suggestion | Cloud cost optimization |

## Code Beacons Sub-Groups

### Entry Points
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| main | Primary entry file. | string; Example: "src/main.ts" | soul.yaml | - |
| api | API entry. | string; Example: "src/api/index.ts" | soul.yaml | - |
| cli | Command-line entry. | string; Example: "src/cli/index.ts" | soul.yaml | - |
| web | Web app entry. | string; Example: "src/web/app.tsx" | soul.yaml | - |
| mobile | Mobile app entry. | string; Example: "src/mobile/App.js" | Future Suggestion | Multi-platform support |

### Business Logic Locations
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| servicesPath | Services directory. | string; Example: "src/domain/services/" | soul.yaml | - |
| validatorsPath | Validators directory. | string; Example: "src/domain/validators/" | soul.yaml | - |
| workflowsPath | Workflows directory. | string; Example: "src/workflows/" | soul.yaml | - |
| keyFiles | Important files list. | array; Example: ["UserService.ts"] | soul.yaml | - |
| aiModulesPath | AI-specific logic. | string; Example: "src/ai/modules/" | Future Suggestion | ML integration |

### Data Layer
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| modelsPath | Data models directory. | string; Example: "src/infrastructure/models/" | soul.yaml | - |
| migrationsPath | Migrations directory. | string; Example: "src/infrastructure/migrations/" | soul.yaml | - |
| repositoriesPath | Repositories directory. | string; Example: "src/infrastructure/repositories/" | soul.yaml | - |
| schemaPath | Database schema files. | string; Example: "db/schemas/" | Future Suggestion | Schema evolution tracking |

### Integrations
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| apisPath | API handlers. | string; Example: "src/api/handlers/" | soul.yaml | - |
| eventsPublishers | Event publishers path. | string; Example: "src/events/publishers/" | soul.yaml | - |
| eventsSubscribers | Event subscribers path. | string; Example: "src/events/subscribers/" | soul.yaml | - |
| externalPath | Third-party integrations. | string; Example: "src/integrations/" | soul.yaml | - |
| webhookPath | Webhook handlers. | string; Example: "src/webhooks/" | Future Suggestion | Real-time integrations |

### Configuration and Testing
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| configPath | Configuration files. | string; Example: "config/" | soul.yaml | - |
| workflowsCiCd | CI/CD workflows. | string; Example: ".github/workflows/" | soul.yaml | - |
| unitTests | Unit test path. | string; Example: "tests/unit/" | soul.yaml | - |
| integrationTests | Integration test path. | string; Example: "tests/integration/" | soul.yaml | - |
| e2eTests | E2E test path. | string; Example: "tests/e2e/" | soul.yaml | - |
| coverage | Coverage report. | string; Example: "coverage/lcov-report/index.html" | soul.yaml | - |
| perfTests | Performance test path. | string; Example: "tests/performance/" | Future Suggestion | Load testing at scale |

### Documentation Supplements
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| technicalDocs | Technical docs path. | string; Example: "docs/technical/" | soul.yaml | - |
| apiDocs | API docs path. | string; Example: "docs/api/" | soul.yaml | - |
| diagrams | Diagrams path. | string; Example: "docs/diagrams/" | soul.yaml | - |
| adr | Architecture Decision Records. | string; Example: "docs/adr/" | soul.yaml | - |
| wikiPath | Internal wiki links. | string; Example: "wiki/pages/" | Future Suggestion | Knowledge base integration |
