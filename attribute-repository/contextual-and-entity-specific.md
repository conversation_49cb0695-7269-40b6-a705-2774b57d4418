# Contextual Dimension and Entity-Specific Attributes

This file contains attributes related to business/technical context and type-specific specs.

## Contextual Dimension Sub-Groups

### Business Reality
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| userImpact | User metrics. | object; Example: {daily_active_users: 50000} | soul.yaml | - |
| revenueImpact | Financial effects. | object; Example: {enabled_revenue: "$2M/month"} | soul.yaml | - |
| riskMitigation | Protected areas. | object; Example: {security: "Prevents unauthorized access"} | soul.yaml | - |
| competitiveAdvantage | Market edges. | object; Example: {differentiator: "10x faster onboarding"} | soul.yaml | - |

### Technical Philosophy
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| designPrinciples | Core principles. | array; Example: [{principle: "Security-first design", implementation: "Zero-trust architecture"}] | soul.yaml | - |
| architecturalPatterns | Patterns used. | array; Example: [{pattern: "Domain-Driven Design", benefit: "Clear bounded contexts"}] | soul.yaml | - |
| antiPatternsAvoided | Avoided pitfalls. | array; Example: [{antiPattern: "God objects", alternative: "Small, focused services"}] | soul.yaml | - |

### Security Profile
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| dataClassification | Data sensitivity. | string; Example: "PII-Confidential" | soul.yaml | - |
| sensitiveData | Sensitive fields. | array; Example: ["email", "phone"] | soul.yaml | - |
| threatModel | Potential threats. | array; Example: [{threat: "SQL Injection", mitigation: "Parameterized queries"}] | soul.yaml | - |
| controls | Security measures. | object; Example: {authentication: {methods: ["Password", "OAuth 2.0"]}} | soul.yaml | - |
| compliance | Standards. | array; Example: [{standard: "GDPR", controls: ["Right to erasure"]}] | soul.yaml | - |

### Observability Monitoring
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| logging | Logging setup. | object; Example: {strategy: "Structured JSON logging"} | soul.yaml | - |
| metrics | Metrics config. | object; Example: {standard: ["RED metrics"]} | soul.yaml | - |
| tracing | Tracing setup. | object; Example: {enabled: true, sampling: "10% in prod"} | soul.yaml | - |
| alerts | Alert rules. | array; Example: [{name: "High Error Rate", condition: "error_rate > 1%"}] | soul.yaml | - |
| dashboards | Dashboard links. | array; Example: [{name: "Service Overview", url: "https://grafana/d/auth-overview"}] | soul.yaml | - |

### Error Handling
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| errorCategories | Error types. | object; Example: {client_errors: [{code: "AUTH001", http_status: 400}]} | soul.yaml | - |
| recoveryStrategies | Recovery plans. | array; Example: [{scenario: "Database connection lost", recovery: "Circuit breaker"}] | soul.yaml | - |
| fallbackBehaviors | Fallback actions. | array; Example: [{trigger: "Rate limit exceeded", behavior: "Return 429"}] | soul.yaml | - |

### Configuration
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| environmentVariables | Env vars. | array; Example: [{name: "NODE_ENV", default: "development"}] | soul.yaml | - |
| featureFlags | Flags config. | array; Example: [{flag: "ENABLE_NEW_AUTH_FLOW", default: false}] | soul.yaml | - |
| secrets | Secrets management. | object; Example: {management: "AWS Secrets Manager"} | soul.yaml | - |
| tuningParameters | Performance params. | array; Example: [{param: "CONNECTION_POOL_SIZE", default: 10}] | soul.yaml | - |

## Entity-Specific Sub-Groups (Varies by Type)

### API Endpoints (For Services/APIs)
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| path | Endpoint path. | string; Example: "/api/v2/login" | soul.yaml | - |
| method | HTTP method. | string; Example: "POST" | soul.yaml | - |
| purpose | Endpoint goal. | string; Example: "User authentication" | soul.yaml | - |
| requestBody | Request details. | object; Example: {contentType: "application/json"} | soul.yaml | - |
| responses | Response details. | object; Example: {200: {description: "Successful authentication"}} | soul.yaml | - |
| rateLimit | Rate limits. | string; Example: "10 requests per minute" | soul.yaml | - |
| sla | Endpoint SLA. | string; Example: "99.99% availability" | soul.yaml | - |

### Event Contracts
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| publishes | Published events. | array; Example: [{event: "user.login.success.v1", schema: {userId: "string"}}] | soul.yaml | - |
| subscribes | Subscribed events. | array; Example: [{event: "user.created.v1", source: "user-service"}] | soul.yaml | - |

### Public API (For Libraries)
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| functions | Library functions. | array; Example: [{name: "validateEmail", signature: "(email: string): ValidationResult"}] | soul.yaml | - |
| types | Defined types. | array; Example: [{name: "ValidationResult", definition: "type ValidationResult = {isValid: boolean; errors?: string[]; }"}] | soul.yaml | - |
| constants | Constants. | array; Example: [{name: "PASSWORD_MIN_LENGTH", value: 12}] | soul.yaml | - |

### Physical Characteristics (For Resources)
| Attribute Name | Description | Data Type/Example | Original Source | Future Use |
|---------------|-------------|------------------|-----------------|------------|
| type | Resource type. | string; Example: "PostgreSQL 14.5" | soul.yaml | - |
| deployment | Deployment details. | object; Example: {provider: "AWS RDS", instance: "db.r5.2xlarge"} | soul.yaml | - |
| networking | Network config. | object; Example: {endpoint: "postgres.internal.example.com:5432"} | soul.yaml | - |
| capacity | Capacity metrics. | object; Example: {connections: 500, storage_used: "650GB"} | soul.yaml | - |
| dataCharacteristics | Data details. | object; Example: {databases: [{name: "users_db", size: "450GB"}]} | soul.yaml | - |
| maintenanceOperations | Maintenance plans. | object; Example: {backups: {frequency: "Daily at 02:00 UTC"}} | soul.yaml | - |
| criticalTables | Key tables. | array; Example: [{table: "users", rows: 10000000}] | soul.yaml | - |

(Future Suggestion) multiRegionSupport: Multi-region config. | object; Example: {regions: ["us-east-1", "eu-west-1"]} | Future Suggestion | Global scaling |
