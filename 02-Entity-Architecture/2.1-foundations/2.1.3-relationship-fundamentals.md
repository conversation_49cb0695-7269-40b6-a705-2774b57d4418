# 2.1.3 Relationship Fundamentals

> **Connecting the Pieces:** You now understand the entity types and hierarchy. This section explains how entities connect to each other through explicit relationships—the "wiring" that makes your architecture work.

## Why Relationships Matter

Imagine you're new to a city and need to find your way around. You could wander randomly, asking strangers for directions each time you get lost. Or you could use a map that clearly shows how streets connect, where the landmarks are, and the best routes between destinations.

Software relationships work the same way—they're the "map" that helps everyone understand how your system's pieces connect and work together.

When relationships between software components are unclear or undocumented, teams face the same problems as someone navigating without a map:
- **Getting Lost:** Developers waste time trying to understand how pieces fit together
- **Breaking Things:** Changes in one area unexpectedly break something else
- **Duplicate Work:** Teams build the same functionality because they don't know it already exists
- **Slow Onboarding:** New team members take months to understand the system instead of days

## The Four Core Relationship Types

The Cortex entity model uses four fundamental relationship types that capture how entities connect and depend on each other:

### 1. Containment Relationships: "What Belongs Where"

**Purpose:** Shows which entities logically contain other entities  
**Direction:** Parent contains child  
**Hospital analogy:** The Cardiology Department contains the Emergency Response service

**Key Patterns:**
- **System contains Features:** A business capability contains the user-facing capabilities that implement it
- **Domain contains Systems:** (Advanced) A business domain contains related systems

**Example:**
```yaml
# User Authentication System contains multiple features
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: user-authentication
spec:
  contains:
    - feature:default/user-registration
    - feature:default/user-login
    - feature:default/password-management
```

**Why this matters:** Containment relationships help you understand scope and ownership. When you need to understand what a System does, you look at the Features it contains.

### 2. Implementation Relationships: "What Builds What"

**Purpose:** Shows which technical components implement business capabilities  
**Direction:** Feature is implemented by Components  
**Hospital analogy:** Emergency Response is implemented by the Ambulance Service, Triage Nurses, and Emergency Room

**Key Patterns:**
- **Feature implementedBy Components:** User-facing capabilities are built using technical components
- **Component implementedBy Components:** (Advanced) Complex components may be implemented by simpler ones

**Example:**
```yaml
# User Login Feature is implemented by multiple components
apiVersion: backstage.io/v1alpha1
kind: Feature
metadata:
  name: user-login
spec:
  implementedBy:
    - component:default/auth-service
    - component:default/session-library
    - component:default/login-ui
```

**Why this matters:** Implementation relationships let you trace from business value to technical implementation. When a Feature has problems, you know exactly which Components to investigate.

### 3. Dependency Relationships: "What Needs What"

**Purpose:** Shows which entities depend on other entities to function  
**Direction:** Dependent depends on dependency  
**Hospital analogy:** The Heart Monitor depends on the Power Grid and the Patient Database

**Key Patterns:**
- **Component dependsOn Component:** One component needs another to function
- **Component dependsOn Resource:** A component needs infrastructure to operate
- **Service dependsOn API:** A service needs to call another service's API

**Example:**
```yaml
# Authentication Service depends on other components and resources
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: auth-service
spec:
  type: service
  dependsOn:
    - component:default/session-library
    - component:default/crypto-library
    - resource:default/user-database
    - resource:default/redis-cache
```

**Why this matters:** Dependency relationships help you understand impact. When a Resource goes down, you can quickly identify which Components will be affected.

### 4. Contract Relationships: "What Talks to What"

**Purpose:** Shows how entities communicate through defined interfaces  
**Direction:** Provider provides API, Consumer consumes API  
**Hospital analogy:** The Lab Service provides a Blood Test API that the Emergency Room consumes

**Key Patterns:**
- **Service providesApis API:** A service exposes an API for others to use
- **Component consumesApis API:** A component uses an API provided by another service
- **API definedBy Component:** An API specification is owned by a specific component

**Example:**
```yaml
# Authentication Service provides an API
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: auth-service
spec:
  type: service
  providesApis:
    - api:default/auth-api

---
# Login UI consumes that API
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: login-ui
spec:
  type: website
  consumesApis:
    - api:default/auth-api
```

**Why this matters:** Contract relationships help you understand communication patterns and manage API changes safely.

## Basic Relationship Patterns

### Pattern 1: Simple Feature Implementation
```
User Registration Feature
├── implementedBy → registration-service (validates and stores user data)
├── implementedBy → email-service (sends verification emails)
└── implementedBy → registration-ui (provides user interface)

registration-service
├── dependsOn → user-database (stores user data)
├── dependsOn → email-library (formats emails)
└── providesApis → registration-api

registration-ui
└── consumesApis → registration-api
```

### Pattern 2: Shared Dependencies
```
Multiple Services sharing a Library:

auth-service
├── dependsOn → crypto-library
└── dependsOn → user-database

password-service  
├── dependsOn → crypto-library (shared)
└── dependsOn → user-database (shared)

profile-service
├── dependsOn → crypto-library (shared)
└── dependsOn → user-database (shared)
```

### Pattern 3: Service Chain
```
API Gateway → Auth Service → User Database

api-gateway
├── consumesApis → auth-api
└── consumesApis → user-api

auth-service
├── providesApis → auth-api
└── dependsOn → user-database

user-service
├── providesApis → user-api
└── dependsOn → user-database
```

## Relationship Documentation Examples

### In catalog-info.yaml Files
```yaml
# System level - shows what it contains
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: user-management
spec:
  contains:
    - feature:default/user-registration
    - feature:default/user-authentication

---
# Feature level - shows what implements it
apiVersion: backstage.io/v1alpha1
kind: Feature
metadata:
  name: user-registration
spec:
  partOf: system:default/user-management
  implementedBy:
    - component:default/registration-service
    - component:default/registration-ui

---
# Component level - shows dependencies and contracts
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: registration-service
spec:
  type: service
  system: user-management
  dependsOn:
    - component:default/email-library
    - resource:default/user-database
  providesApis:
    - api:default/registration-api
  consumesApis:
    - api:default/notification-api
```

## Common Relationship Mistakes

### Mistake 1: Circular Dependencies
**Problem:** Component A depends on Component B, which depends on Component A
```yaml
# DON'T DO THIS
service-a:
  dependsOn: [component:default/service-b]
service-b:
  dependsOn: [component:default/service-a]  # Creates a circle!
```

**Solution:** Introduce a shared library or redesign the interaction pattern

### Mistake 2: Missing Dependencies
**Problem:** Component uses another component but doesn't document the dependency
```yaml
# INCOMPLETE - missing database dependency
auth-service:
  providesApis: [api:default/auth-api]
  # Missing: dependsOn user-database
```

**Solution:** Document all dependencies, even "obvious" ones

### Mistake 3: Wrong Relationship Direction
**Problem:** Using the wrong direction for relationships
```yaml
# WRONG - Features don't contain Systems
user-login:
  contains: [system:default/user-management]  # Backwards!

# CORRECT - Systems contain Features  
user-management:
  contains: [feature:default/user-login]
```

### Mistake 4: Mixing Relationship Types
**Problem:** Confusing implementation with dependency
```yaml
# CONFUSING - mixing concepts
user-service:
  implementedBy: [resource:default/user-database]  # Wrong - should be dependsOn
  dependsOn: [component:default/user-ui]          # Wrong - should be implementedBy
```

## Validation Questions

Use these questions to validate your relationships:

**For Containment:**
- Does the parent entity logically own or encompass the child?
- Would the child make sense without the parent's context?

**For Implementation:**
- Does the component actually contribute to implementing the feature?
- Could the feature work without this component?

**For Dependencies:**
- Would the dependent entity fail if the dependency was unavailable?
- Is this a runtime dependency, not just a development-time relationship?

**For Contracts:**
- Is there actual communication happening through this API?
- Are both the provider and consumer documented?

## Foundation for Practical Application

Understanding these four relationship types gives you the foundation for practical application. In the next section, you'll learn step-by-step techniques for:

1. **Documenting relationships explicitly** - Making all connections visible and traceable
2. **Designing loose coupling** - Keeping components independent and flexible
3. **Organizing hierarchies** - Structuring entities for maximum clarity
4. **Managing API versions** - Evolving contracts without breaking existing users

Each of these practices builds on the relationship fundamentals you've learned here.

## Next Steps

You now have the foundational knowledge needed to start applying the Cortex entity model. Choose your path based on your learning goals:

**→ Continue Learning Path (Recommended):**
- **[2.2.1 Documenting Relationships](../2.2-practical-application/2.2.1-documenting-relationships.md)** - Learn step-by-step techniques for making relationships explicit

**Practical Application:**
- **[2.2.2 Designing Loose Coupling](../2.2-practical-application/2.2.2-designing-loose-coupling.md)** - Learn to minimize dependencies and create flexible architectures
- **[2.2.3 Organizing Hierarchies](../2.2-practical-application/2.2.3-organizing-hierarchies.md)** - Structure entities for maximum clarity and maintainability
- **[2.2.4 Managing API Versions](../2.2-practical-application/2.2.4-managing-api-versions.md)** - Evolve contracts safely without breaking consumers
- **[2.2.5 Hands-On Exercises](../2.2-practical-application/2.2.5-hands-on-exercises.md)** - Practice with real scenarios and validate your understanding

**Advanced Framework:**
- **[2.3.1 Essential Dimensions Overview](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)** - Master comprehensive entity documentation

**Quick Reference:**
- **[2.4.1 Quick Reference Cards](../2.4-reference/2.4.1-quick-reference-cards.md)** - Relationship types, decision trees, and validation checklists
- **[2.4.2 Troubleshooting Guide](../2.4-reference/2.4.2-troubleshooting-guide.md)** - Solutions for common relationship problems

**Review Foundations:**
- **[2.1.1 Entity Model Introduction](./2.1.1-entity-model-introduction.md)** - Review why entity modeling matters
- **[2.1.2 Entity Types and Hierarchy](./2.1.2-entity-types-and-hierarchy.md)** - Review entity types and hierarchy rules

---

*Remember: Relationships are the "wiring diagram" of your architecture. Just like an electrician needs to understand how circuits connect, software architects need to understand how components relate. Start by documenting the relationships you can see clearly, then gradually fill in the gaps as your understanding grows.*