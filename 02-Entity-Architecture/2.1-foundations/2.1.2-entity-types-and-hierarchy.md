# 2.1.2 Entity Types and Hierarchy

> **Building on Foundations:** Now that you understand why entity modeling matters and the basic concepts, let's explore the specific types of entities and how they organize into clear hierarchies.

## The Entity Hierarchy: From Business Value to Technical Implementation

The Cortex entity model follows a clear hierarchy that mirrors how businesses actually organize and deliver value:

```
System (Business Capability)
  └── contains → Features (User Value)
        └── implementedBy → Components (Technical Implementation)
              └── dependsOn → Resources (Infrastructure)
```

This hierarchy ensures that every piece of technical implementation can be traced back to business value, and every business capability can be understood in terms of its technical requirements.

## Top-Level Business Entities

### System: The Strategic Container

**What it represents:** A major business capability or architectural domain  
**Hospital analogy:** The entire Cardiology Department  
**Purpose:** Groups all Features and Components that work together to deliver significant business value

#### Characteristics of a Good System
- **Business-Focused:** Represents a clear business capability that stakeholders understand
- **Strategic Scope:** Large enough to justify dedicated ownership and investment
- **Bounded Context:** Has clear boundaries that separate it from other business capabilities
- **Value-Oriented:** Delivers measurable business value that can be tracked and optimized

#### System Examples
```yaml
# User Authentication System
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: user-authentication
  description: "Centralized user identity and access management"
spec:
  owner: platform-team
  domain: identity-management
```

**Real-world System examples:**
- **E-commerce Platform:** Product Catalog System, Shopping Cart System, Payment Processing System
- **Healthcare:** Patient Management System, Billing System, Medical Records System  
- **Financial Services:** Account Management System, Transaction Processing System, Risk Assessment System

### Feature: The Business-Technology Bridge

**What it represents:** A specific, user-facing capability within a System  
**Hospital analogy:** Emergency Heart Attack Response or Routine Checkups  
**Purpose:** Connects high-level business context to technical implementation

#### Characteristics of a Good Feature
- **User-Centric:** Delivers direct value to end users or other systems
- **Cohesive:** Represents a complete capability that makes sense to users
- **Testable:** Can be validated through user acceptance criteria
- **Traceable:** Can be traced from business requirements to technical implementation

#### Feature Examples
```yaml
# User Login Feature
apiVersion: backstage.io/v1alpha1
kind: Feature
metadata:
  name: user-login
  description: "Secure user authentication capability"
spec:
  owner: product-team
  partOf: system:default/user-authentication
```

**Feature breakdown for User Authentication System:**
- **User Registration Feature:** Account creation, email verification, profile setup
- **User Login Feature:** Credential validation, session management, multi-factor authentication
- **Password Management Feature:** Password reset, password policies, security questions

## Technical Implementation Entities

### Component: The Building Blocks

Components are the fundamental technical units that implement Features. Each component type serves a specific architectural role and has distinct characteristics.

#### Service: The Active Performers

**Definition:** Long-running applications that can be called over a network  
**Hospital analogy:** Ambulance Service, EKG Monitoring Service  
**Purpose:** Actively processes requests, manages state, and provides functionality through APIs

**Characteristics:**
- **Always Running:** Deployed as persistent processes that handle requests
- **Network Accessible:** Can be called by other components over HTTP, gRPC, or other protocols
- **Stateful or Stateless:** May manage persistent state or be purely computational
- **API Providers:** Typically expose APIs for other components to consume

**Service Examples:**
```yaml
# Authentication Service
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: auth-service
  description: "Core authentication and session management service"
spec:
  type: service
  owner: backend-team
  system: user-authentication
  providesApis:
    - api:default/auth-api
  dependsOn:
    - resource:default/user-database
```

**Common Service patterns:**
- **Microservices:** Small, focused services with single responsibilities
- **API Gateways:** Services that route and manage API traffic
- **Background Workers:** Services that process queues or scheduled tasks
- **Data Services:** Services that manage specific data domains

#### API: The Communication Contracts

**Definition:** Formal, versioned contracts for how components communicate  
**Hospital analogy:** Standardized Triage Checklist, Blood Test Request Form  
**Purpose:** Defines interfaces without implementing them—the "user manual" for component interaction

**Characteristics:**
- **Contract-Only:** Specifies interface without implementation
- **Versioned:** Maintains backward compatibility through versioning
- **Technology-Agnostic:** Can be implemented in any technology
- **Discoverable:** Can be found and understood by potential consumers

**API Examples:**
```yaml
# Authentication API
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: auth-api
  description: "Authentication and session management API"
spec:
  type: openapi
  owner: backend-team
  system: user-authentication
  definition: |
    openapi: 3.0.0
    info:
      title: Authentication API
      version: 1.0.0
```

**API Types:**
- **REST APIs:** HTTP-based APIs following REST principles
- **GraphQL APIs:** Query-based APIs with flexible data fetching
- **gRPC APIs:** High-performance RPC APIs with strong typing
- **Event APIs:** Asynchronous message-based APIs

#### Library: The Reusable Logic

**Definition:** Bundles of reusable code consumed by other components  
**Hospital analogy:** Stent Placement Protocol Library—validated procedures used across services  
**Purpose:** Encapsulates shared business logic and technical utilities

**Characteristics:**
- **Code Artifacts:** Distributed as packages, JARs, NPM modules, etc.
- **Reusable:** Designed to be used by multiple components
- **Versioned:** Follows semantic versioning for compatibility
- **Focused:** Addresses specific, well-defined functionality

**Library Examples:**
```yaml
# Session Management Library
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: session-library
  description: "Shared session management utilities"
spec:
  type: library
  owner: platform-team
  system: user-authentication
```

**Library Categories:**
- **Business Logic Libraries:** Domain-specific rules and calculations
- **Utility Libraries:** Common technical functions (logging, validation, etc.)
- **Integration Libraries:** Wrappers for external services or databases
- **UI Component Libraries:** Reusable user interface elements

#### Website: The User Interfaces

**Definition:** User-facing web applications  
**Hospital analogy:** Patient Portal, Doctor's Dashboard  
**Purpose:** Provides human interface to systems, translating backend functionality into accessible user experiences

**Characteristics:**
- **User-Facing:** Designed for human interaction
- **Frontend Technology:** Built with web technologies (React, Vue, Angular, etc.)
- **Consumer of APIs:** Typically consumes backend services through APIs
- **Responsive:** Adapts to different devices and screen sizes

**Website Examples:**
```yaml
# Login Portal
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: login-portal
  description: "User authentication web interface"
spec:
  type: website
  owner: frontend-team
  system: user-authentication
  consumesApis:
    - api:default/auth-api
```

**Website Types:**
- **Single Page Applications (SPAs):** Dynamic, client-side rendered applications
- **Server-Side Rendered:** Traditional web applications with server rendering
- **Static Sites:** Pre-built sites with minimal dynamic functionality
- **Progressive Web Apps:** Web applications with native app-like features

#### Resource: The Infrastructure Dependencies

**Definition:** Infrastructure pieces that components depend on  
**Hospital analogy:** Power Grid, Central Pharmacy Database, Oxygen Supply System  
**Purpose:** Enables code to function by providing essential infrastructure services

**Characteristics:**
- **Not Code:** Infrastructure, not application code
- **External Dependencies:** Managed outside the application lifecycle
- **Shared:** Often used by multiple components
- **Operational:** Requires infrastructure management and monitoring

**Resource Examples:**
```yaml
# User Database
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: user-database
  description: "PostgreSQL database for user data"
spec:
  type: database
  owner: platform-team
  system: user-authentication
```

**Resource Categories:**
- **Databases:** PostgreSQL, MongoDB, Redis, etc.
- **Message Queues:** RabbitMQ, Apache Kafka, AWS SQS, etc.
- **Storage Systems:** S3 buckets, file systems, CDNs, etc.
- **External Services:** Third-party APIs, SaaS platforms, etc.

## Hierarchy Rules and Principles

### Rule 1: Clear Ownership Boundaries
Each level of the hierarchy should have clear ownership:
- **Systems:** Owned by product or platform teams
- **Features:** Owned by product teams or feature teams  
- **Components:** Owned by development teams
- **Resources:** Owned by platform or infrastructure teams

### Rule 2: Logical Containment
Higher-level entities should logically contain lower-level entities:
- Systems contain related Features that work together
- Features are implemented by Components that collaborate
- Components depend on Resources they need to function

### Rule 3: Appropriate Abstraction Levels
Each level should operate at the appropriate level of abstraction:
- **Systems:** Business capabilities and strategic value
- **Features:** User-facing functionality and acceptance criteria
- **Components:** Technical implementation and architectural patterns
- **Resources:** Infrastructure requirements and operational concerns

### Rule 4: Traceability
You should be able to trace from any level to any other level:
- From business requirement → System → Feature → Component
- From technical issue → Component → Feature → System → business impact
- From user story → Feature → implementing Components

## Common Hierarchy Patterns

### Pattern 1: Microservices Architecture
```
E-commerce System
├── Product Catalog Feature
│   ├── catalog-service (Service)
│   ├── catalog-api (API)
│   └── product-database (Resource)
├── Shopping Cart Feature
│   ├── cart-service (Service)
│   ├── cart-ui (Website)
│   └── cart-cache (Resource)
└── Checkout Feature
    ├── checkout-service (Service)
    ├── payment-library (Library)
    └── checkout-ui (Website)
```

### Pattern 2: Monolithic with Shared Resources
```
Legacy Application System
├── User Management Feature
│   └── monolith-app (Service - partial)
├── Reporting Feature
│   └── monolith-app (Service - partial)
└── Shared Resources
    ├── main-database (Resource)
    └── file-storage (Resource)
```

### Pattern 3: Library-Centric Architecture
```
Data Processing System
├── Data Ingestion Feature
│   ├── ingestion-service (Service)
│   └── data-validators (Library)
├── Data Transformation Feature
│   ├── transform-service (Service)
│   ├── transform-rules (Library)
│   └── data-validators (Library - shared)
└── Data Export Feature
    ├── export-service (Service)
    └── format-converters (Library)
```

## Validation Checklist

Use this checklist to validate your entity hierarchy:

**System Level:**
- [ ] Represents a clear business capability
- [ ] Has dedicated ownership
- [ ] Contains related Features that work together
- [ ] Provides measurable business value

**Feature Level:**
- [ ] Delivers direct user value
- [ ] Can be tested through acceptance criteria
- [ ] Is implemented by a cohesive set of Components
- [ ] Maps to user stories or business requirements

**Component Level:**
- [ ] Has a single, clear technical responsibility
- [ ] Fits one of the five component types
- [ ] Has appropriate dependencies
- [ ] Is owned by a development team

**Resource Level:**
- [ ] Represents infrastructure, not application code
- [ ] Is shared appropriately across components
- [ ] Has operational management defined
- [ ] Has clear availability and performance requirements

## Next Steps

Now that you understand the entity types and hierarchy, you're ready to learn about how these entities relate to each other through explicit relationships.

**→ Continue Learning Path:**
- **[2.1.3 Relationship Fundamentals](./2.1.3-relationship-fundamentals.md)** - Learn how entities connect and depend on each other

**Apply Your Knowledge:**
- **[2.2.1 Documenting Relationships](../2.2-practical-application/2.2.1-documenting-relationships.md)** - Step-by-step guide to documenting entity relationships
- **[2.2.3 Organizing Hierarchies](../2.2-practical-application/2.2.3-organizing-hierarchies.md)** - Practical guide to organizing complex hierarchies
- **[2.2.5 Hands-On Exercises](../2.2-practical-application/2.2.5-hands-on-exercises.md)** - Practice hierarchy organization with exercises

**Advanced Learning:**
- **[2.3.1 Essential Dimensions Overview](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)** - Complete framework for advanced architecture modeling

**Quick Reference:**
- **[2.4.1 Quick Reference Cards](../2.4-reference/2.4.1-quick-reference-cards.md)** - Entity hierarchy decision trees and validation checklists
- **[2.4.2 Troubleshooting Guide](../2.4-reference/2.4.2-troubleshooting-guide.md)** - Solutions for hierarchy organization problems

**Review Previous Concepts:**
- **[2.1.1 Entity Model Introduction](./2.1.1-entity-model-introduction.md)** - Review the foundational concepts and hospital analogy

---

*Remember: The hierarchy isn't just about organization—it's about creating clear paths from business value to technical implementation. Every component should trace back to user value, and every business capability should be implementable through concrete technical components.*