# Quick Reference Cards: Entity Architecture

> **Quick Access Hub**: Essential reference materials for applying entity architecture concepts in practice. Use this as your go-to resource for decision-making, troubleshooting, and validation.

## 📋 Table of Contents

- [Core Concepts Quick Reference](#-core-concepts-quick-reference)
- [Decision Trees](#-decision-trees)
- [Validation Checklists](#-validation-checklists)
- [Assessment Scorecards](#-assessment-scorecards)
- [Troubleshooting Guides](#-troubleshooting-guides)
- [Learning Path Reference](#-learning-path-reference)

---

## 🎯 Core Concepts Quick Reference

### Relationship Types Reference Card

| Relationship | Purpose | Example | When to Use |
|-------------|---------|---------|-------------|
| `dependsOn` | "I need this to function" | Service → Database | Component requires another component/resource to operate |
| `consumesApis` | "I call this API" | Web App → User API | Component makes API calls to another service |
| `providesApis` | "I offer this API" | Auth Service → Auth API | Component exposes APIs for others to consume |
| `implementedBy` | "These components provide this feature" | User Management → User Service | Feature is realized by specific components |
| `contains` | "This system includes these features" | E-commerce → Shopping Cart | System groups related features together |

### Entity Hierarchy Quick Reference

```
System (Business Domain)
  └── contains → Feature (User Capability)
        └── implementedBy → Component (Technical Implementation)
              └── dependsOn → Resource (Infrastructure)
```

**Golden Rules:**
- Systems contain Features (not Components directly)
- Features are implemented by Components (not Systems)
- Components depend on Resources and other Components
- No circular containment relationships

### Coupling Health Indicators

| 🚨 Red Flags | 🟡 Yellow Flags | ✅ Green Flags |
|-------------|----------------|---------------|
| Services share databases | Services call each other directly | Services use APIs only |
| Circular dependencies | Large shared libraries | Focused, single-purpose libraries |
| Can't deploy independently | Tight release coordination needed | Independent deployment possible |
| Changes break other services | Changes require extensive testing | Changes have predictable impact |

---

## 🌳 Decision Trees

### 1. Relationship Documentation Decision Tree

```
New Component Created
├─ Does it need a database?
│   └─ YES → Add database to `dependsOn`
├─ Does it call other services?
│   └─ YES → Add services to `consumesApis`
├─ Does it expose APIs?
│   └─ YES → Add APIs to `providesApis`
├─ Does it use libraries?
│   └─ YES → Add libraries to `dependsOn`
├─ Does it implement a feature?
│   └─ YES → Feature uses `implementedBy` for this component
└─ Does it belong to a system?
    └─ YES → System uses `contains` for related features
```

### 2. Coupling Problem Resolution Decision Tree

```
Coupling Problem Identified
├─ Services share database?
│   └─ Solution: Split databases, add APIs for data access
├─ Circular dependencies?
│   └─ Solution: Extract shared logic or redesign service boundaries
├─ Direct method calls between services?
│   └─ Solution: Replace with REST APIs or event-driven communication
├─ Monolithic shared library?
│   └─ Solution: Split into focused, single-purpose libraries
└─ Can't deploy services independently?
    └─ Solution: Identify and break blocking dependencies
```

### 3. Entity Hierarchy Organization Decision Tree

```
New Entity to Organize
├─ Does it serve end users directly?
│   └─ YES → This is a Feature
├─ Does it group related user capabilities?
│   └─ YES → This is a System
├─ Is it a technical implementation?
│   └─ YES → This is a Component
├─ Is it infrastructure or external dependency?
│   └─ YES → This is a Resource
└─ Still unsure?
    └─ Start with Component, refactor later as understanding improves
```

### 4. API Change Impact Decision Tree

```
API Change Needed
├─ Will this break existing consumers?
│   ├─ YES → Major version bump (v2.0), create migration plan
│   └─ NO → Continue to next check
├─ Does this add new required functionality?
│   ├─ YES → Minor version bump (v1.1)
│   └─ NO → Continue to next check
├─ Is this a bug fix without interface change?
│   └─ YES → Patch version bump (v1.0.1)
└─ Deploy with appropriate versioning strategy
```

### 5. Architecture Review Decision Tree

```
Architecture Review Needed
├─ Is this a new system or major feature?
│   └─ YES → Full architecture review with stakeholders
├─ Does this change system boundaries?
│   └─ YES → Review with affected teams and architects
├─ Does this introduce new dependencies?
│   └─ YES → Review dependency impact and alternatives
├─ Does this change existing APIs?
│   └─ YES → Review with API consumers and versioning strategy
└─ Standard component change → Team-level review sufficient
```

---

## ✅ Validation Checklists

### Pre-Deployment Relationship Checklist

**Dependencies Documentation:**
- [ ] All database dependencies documented in `dependsOn`
- [ ] All library dependencies documented in `dependsOn`
- [ ] All external service dependencies documented in `dependsOn`
- [ ] All infrastructure dependencies documented in `dependsOn`

**API Documentation:**
- [ ] All API calls documented in `consumesApis`
- [ ] All exposed APIs documented in `providesApis`
- [ ] API versions specified for all relationships
- [ ] API contracts documented and up-to-date

**Relationship Quality:**
- [ ] All relationships have explanatory comments
- [ ] Relationship names are specific (not generic like "database")
- [ ] Comments explain WHY, not just WHAT
- [ ] No "mystery" dependencies that aren't documented

### Coupling Assessment Checklist

**Database Independence:**
- [ ] Each service owns its data
- [ ] No shared databases between services
- [ ] Data access happens through APIs, not direct database access
- [ ] Database schemas are service-specific

**Deployment Independence:**
- [ ] Services can be deployed separately
- [ ] No coordinated releases required
- [ ] Service failures don't cascade to other services
- [ ] Rollback can happen independently

**Communication Patterns:**
- [ ] Services communicate through APIs or events
- [ ] No direct method calls between services
- [ ] Clear interface contracts defined
- [ ] Asynchronous communication where appropriate

**Library Design:**
- [ ] Libraries are focused and single-purpose
- [ ] No monolithic shared libraries
- [ ] Services include only what they need
- [ ] Library versions are managed independently

### Hierarchy Validation Checklist

**Structure Validation:**
- [ ] Systems contain Features (not Components directly)
- [ ] Features are implemented by Components (not Systems)
- [ ] Components depend on Resources and other Components
- [ ] No circular containment (A contains B, B contains A)

**Ownership and Purpose:**
- [ ] Each entity has a clear owner
- [ ] Each entity has a well-defined purpose
- [ ] Entity boundaries align with team boundaries
- [ ] Responsibilities don't overlap between entities

**Business Alignment:**
- [ ] Systems represent business domains
- [ ] Features represent user-facing capabilities
- [ ] Components represent technical implementations
- [ ] Resources represent infrastructure dependencies

### API Versioning Checklist

**Version Planning:**
- [ ] Breaking changes identified and documented
- [ ] Migration plan created for consumers
- [ ] Deprecation timeline established
- [ ] Backward compatibility strategy defined

**Communication:**
- [ ] All consumers notified of upcoming changes
- [ ] Migration documentation provided
- [ ] Support channels established for migration help
- [ ] Timeline communicated clearly

**Implementation:**
- [ ] New version deployed alongside old version
- [ ] Deprecation warnings added to old version
- [ ] Migration metrics tracked
- [ ] Rollback plan prepared

---

## 📊 Assessment Scorecards

### Relationship Documentation Maturity Scorecard

Rate each area 1-5 (1=Poor, 5=Excellent):

**Completeness** ___/5
- [ ] All dependencies documented
- [ ] All APIs documented  
- [ ] All resources documented
- [ ] No undocumented relationships

**Clarity** ___/5
- [ ] Specific, descriptive names used
- [ ] Clear explanatory comments provided
- [ ] Easy to understand for newcomers
- [ ] Consistent terminology throughout

**Accuracy** ___/5
- [ ] Documentation matches actual code
- [ ] Updated when code changes
- [ ] No outdated relationships
- [ ] No incorrect relationships

**Usefulness** ___/5
- [ ] Helps with debugging issues
- [ ] Speeds up onboarding process
- [ ] Enables confident changes
- [ ] Supports impact analysis

**Total Score: ___/20**

**Scoring Guide:**
- 16-20: Excellent relationship documentation
- 12-15: Good, minor improvements needed
- 8-11: Adequate, significant improvements needed
- 4-7: Poor, major overhaul required
- 0-3: Critical, start documenting immediately

### Coupling Health Scorecard

Rate each area 1-5 (1=Tightly Coupled, 5=Loosely Coupled):

**Database Independence** ___/5
- [ ] Services own their data
- [ ] No shared databases
- [ ] APIs used for data access
- [ ] Independent data evolution

**Deployment Independence** ___/5
- [ ] Services deploy separately
- [ ] No coordinated releases required
- [ ] Changes don't break other services
- [ ] Independent scaling possible

**Library Design** ___/5
- [ ] Focused, single-purpose libraries
- [ ] No monolithic shared libraries
- [ ] Services include only what they need
- [ ] Independent library evolution

**Communication Patterns** ___/5
- [ ] APIs or events for communication
- [ ] No direct method calls
- [ ] Clear interface contracts
- [ ] Graceful failure handling

**Total Score: ___/20**

**Scoring Guide:**
- 16-20: Excellent loose coupling
- 12-15: Good coupling practices
- 8-11: Some coupling issues to address
- 4-7: Significant coupling problems
- 0-3: Tightly coupled, needs major refactoring

### Architecture Maturity Scorecard

Rate each area 1-5 (1=Immature, 5=Mature):

**Documentation Quality** ___/5
- [ ] Complete and accurate documentation
- [ ] Regular updates and maintenance
- [ ] Clear and accessible format
- [ ] Useful for decision-making

**Team Practices** ___/5
- [ ] Consistent documentation practices
- [ ] Regular architecture reviews
- [ ] Clear ownership and responsibilities
- [ ] Effective knowledge sharing

**Technical Excellence** ___/5
- [ ] Well-designed system boundaries
- [ ] Appropriate coupling levels
- [ ] Clear dependency management
- [ ] Scalable architecture patterns

**Business Alignment** ___/5
- [ ] Architecture supports business goals
- [ ] Clear value delivery paths
- [ ] Adaptable to business changes
- [ ] Measurable business impact

**Total Score: ___/20**

**Scoring Guide:**
- 16-20: Mature architecture practice
- 12-15: Good foundation, some improvements needed
- 8-11: Developing practice, focus on key areas
- 4-7: Early stage, significant investment needed
- 0-3: Ad-hoc approach, establish basic practices

---

## 🛠️ Troubleshooting Guides

### "My Service Keeps Failing" Debug Guide

**Step 1: Check Dependencies**
- [ ] Are all `dependsOn` components running and healthy?
- [ ] Can you connect to all databases and external services?
- [ ] Are all required environment variables set?
- [ ] Are network connections working properly?

**Step 2: Verify API Connections**
- [ ] Are all `consumesApis` endpoints responding?
- [ ] Are you using the correct API versions?
- [ ] Are authentication credentials valid?
- [ ] Are rate limits being respected?

**Step 3: Review Recent Changes**
- [ ] Did any dependencies change recently?
- [ ] Were any APIs updated or deprecated?
- [ ] Did infrastructure configuration change?
- [ ] Were any libraries updated?

**Step 4: Analyze Error Patterns**
- [ ] Are errors consistent or intermittent?
- [ ] Do errors correlate with specific operations?
- [ ] Are there patterns in timing or load?
- [ ] What do the logs reveal about root cause?

### "I Don't Know What Will Break" Impact Analysis Guide

**Step 1: Identify Consumers**
- [ ] Who uses your `providesApis`?
- [ ] What services have you in their `dependsOn`?
- [ ] Which teams depend on your service?
- [ ] Are there any undocumented dependencies?

**Step 2: Assess Change Impact**
- [ ] Will this change break existing APIs?
- [ ] Will this affect service availability?
- [ ] Will this change data formats or schemas?
- [ ] Will this affect performance characteristics?

**Step 3: Create Test Scenarios**
- [ ] Test each relationship individually
- [ ] Verify backward compatibility
- [ ] Test failure scenarios and recovery
- [ ] Validate performance under load

**Step 4: Plan Communication and Rollback**
- [ ] Notify all affected teams
- [ ] Prepare rollback procedures
- [ ] Set up monitoring for impact detection
- [ ] Have support channels ready

### "New Person Can't Understand System" Onboarding Guide

**Step 1: Start with the Big Picture**
- [ ] Show system-level architecture
- [ ] Explain business domains and purposes
- [ ] Identify key user journeys
- [ ] Highlight critical system boundaries

**Step 2: Drill into Features**
- [ ] Explain user-facing capabilities
- [ ] Show how features connect to business value
- [ ] Identify feature owners and stakeholders
- [ ] Demonstrate feature interactions

**Step 3: Explore Components**
- [ ] Walk through technical implementation
- [ ] Explain component responsibilities
- [ ] Show how components implement features
- [ ] Identify technical dependencies

**Step 4: Follow the Data Flow**
- [ ] Trace requests through the system
- [ ] Show data transformation points
- [ ] Explain integration patterns
- [ ] Identify potential failure points

### "Teams Keep Breaking Each Other" Coordination Guide

**Step 1: Establish Clear Contracts**
- [ ] Document all APIs explicitly
- [ ] Define service level agreements
- [ ] Establish communication protocols
- [ ] Create change notification processes

**Step 2: Implement Proper Versioning**
- [ ] Use semantic versioning for all APIs
- [ ] Maintain backward compatibility
- [ ] Provide migration guides
- [ ] Give adequate deprecation notice

**Step 3: Improve Testing and Validation**
- [ ] Implement contract testing
- [ ] Set up integration test suites
- [ ] Create staging environment validation
- [ ] Monitor production impact

**Step 4: Enhance Communication**
- [ ] Regular cross-team sync meetings
- [ ] Shared documentation and updates
- [ ] Clear escalation procedures
- [ ] Post-incident learning sessions

---

## 🎓 Learning Path Reference

### Beginner Path (0-3 months)
**Foundation Building:**
- [ ] Read [Entity Model Introduction](../2.1-foundations/2.1.1-entity-model-introduction.md)
- [ ] Study [Entity Types and Hierarchy](../2.1-foundations/2.1.2-entity-types-and-hierarchy.md)
- [ ] Learn [Relationship Fundamentals](../2.1-foundations/2.1.3-relationship-fundamentals.md)

**First Practice:**
- [ ] Document relationships for one existing service
- [ ] Use relationship types reference card
- [ ] Complete beginner exercises from [Hands-On Exercises](../2.2-practical-application/2.2.5-hands-on-exercises.md)

**Validation:**
- [ ] Use pre-deployment checklist for your service
- [ ] Score your work with relationship documentation scorecard
- [ ] Get feedback from experienced team member

### Intermediate Path (3-12 months)
**Practical Application:**
- [ ] Study [Documenting Relationships](../2.2-practical-application/2.2.1-documenting-relationships.md)
- [ ] Learn [Designing Loose Coupling](../2.2-practical-application/2.2.2-designing-loose-coupling.md)
- [ ] Practice [Organizing Hierarchies](../2.2-practical-application/2.2.3-organizing-hierarchies.md)
- [ ] Master [Managing API Versions](../2.2-practical-application/2.2.4-managing-api-versions.md)

**Leadership Practice:**
- [ ] Lead relationship documentation for your team
- [ ] Identify and fix coupling problems in existing systems
- [ ] Plan and execute an API version migration
- [ ] Mentor junior developers using these materials

**Validation:**
- [ ] Use coupling assessment checklist on team's services
- [ ] Score team's architecture with maturity scorecard
- [ ] Successfully complete intermediate exercises

### Advanced Path (1+ years)
**Comprehensive Understanding:**
- [ ] Study [Essential Dimensions Overview](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)
- [ ] Master [Dimensional Documentation](../2.3-comprehensive-framework/2.3.2-dimensional-documentation.md)
- [ ] Apply [Implementation Patterns](../2.3-comprehensive-framework/2.3.3-implementation-patterns.md)

**Organizational Impact:**
- [ ] Establish organization-wide relationship standards
- [ ] Design tooling for relationship discovery and validation
- [ ] Lead major architectural refactoring initiatives
- [ ] Create training materials and processes for your organization

**Validation:**
- [ ] Successfully architect complex multi-team systems
- [ ] Achieve high scores on all assessment scorecards
- [ ] Contribute to architectural decision-making processes

### Expert Path (2+ years)
**Innovation and Leadership:**
- [ ] Research and experiment with new architectural patterns
- [ ] Contribute to open source architectural tools
- [ ] Speak at conferences about architectural practices
- [ ] Influence industry best practices

**Mentorship and Teaching:**
- [ ] Mentor other architects and senior developers
- [ ] Create advanced training programs
- [ ] Write articles and documentation for broader community
- [ ] Lead architectural communities of practice

---

## 🎯 Success Metrics and Indicators

### Individual Success Indicators
- ✅ Can document any component's relationships completely and accurately
- ✅ Can identify coupling problems and design effective solutions
- ✅ Can organize components into proper hierarchy that aligns with business value
- ✅ Can plan and execute safe API migrations without breaking consumers
- ✅ Can onboard new team members quickly using clear documentation

### Team Success Indicators
- ✅ All services have complete and up-to-date relationship documentation
- ✅ Services can be deployed independently without coordination
- ✅ New team members become productive within days, not weeks
- ✅ API changes don't cause surprise breakages for consumers
- ✅ System architecture is easy to understand and navigate

### Organization Success Indicators
- ✅ Consistent relationship documentation practices across all teams
- ✅ Minimal coupling between team boundaries and services
- ✅ Clear system ownership and well-defined boundaries
- ✅ Smooth API evolution without breaking existing consumers
- ✅ Fast onboarding and effective knowledge transfer across teams

---

## 🔗 Cross-References

### Related Learning Materials
- **Foundations**: [Entity Architecture Foundations](../2.1-foundations/2.1.1-entity-model-introduction.md)
- **Practical Application**: [Hands-On Learning](../2.2-practical-application/2.2.1-documenting-relationships.md)
- **Comprehensive Framework**: [Advanced Concepts](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)
- **Additional References**: [Troubleshooting Guide](./2.4.2-troubleshooting-guide.md) | [Complete Examples](./2.4.3-complete-examples.md)

### External Resources
- **Backstage Documentation**: For catalog-info.yaml specifications
- **API Design Guidelines**: For versioning and contract design best practices
- **Microservices Patterns**: For advanced coupling and communication patterns
- **Domain-Driven Design**: For system and feature boundary identification

---

*Remember: These reference cards are tools to help you apply entity architecture concepts in practice. The real learning happens when you use these techniques on real systems with real constraints and real teammates. Start with the basics, practice regularly, and gradually work your way up to more advanced concepts.*