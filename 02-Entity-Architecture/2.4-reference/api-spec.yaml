openapi: 3.0.0
info:
  title: User Service API
  description: Example API specification for the User Service component
  version: 1.0.0
  contact:
    name: TechMart Development Team
    email: <EMAIL>

servers:
  - url: https://api.techmart.com/v1
    description: Production server
  - url: https://staging-api.techmart.com/v1
    description: Staging server

paths:
  /users:
    get:
      summary: List users
      description: Retrieve a paginated list of users
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of users per page
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          description: Unauthorized
        '429':
          description: Rate limit exceeded

    post:
      summary: Create user
      description: Create a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Invalid request data
        '401':
          description: Unauthorized
        '409':
          description: User already exists

  /users/{userId}:
    get:
      summary: Get user by ID
      description: Retrieve a specific user by their ID
      parameters:
        - name: userId
          in: path
          required: true
          description: Unique identifier for the user
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '404':
          description: User not found

    put:
      summary: Update user
      description: Update an existing user's information
      parameters:
        - name: userId
          in: path
          required: true
          description: Unique identifier for the user
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Invalid request data
        '401':
          description: Unauthorized
        '404':
          description: User not found

    delete:
      summary: Delete user
      description: Delete a user account
      parameters:
        - name: userId
          in: path
          required: true
          description: Unique identifier for the user
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: User deleted successfully
        '401':
          description: Unauthorized
        '404':
          description: User not found

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
        email:
          type: string
          format: email
          description: User's email address
        username:
          type: string
          description: User's chosen username
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        createdAt:
          type: string
          format: date-time
          description: When the user account was created
        updatedAt:
          type: string
          format: date-time
          description: When the user account was last updated
        isActive:
          type: boolean
          description: Whether the user account is active
      required:
        - id
        - email
        - username
        - firstName
        - lastName
        - createdAt
        - updatedAt
        - isActive

    CreateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        username:
          type: string
          description: User's chosen username
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        password:
          type: string
          format: password
          description: User's password
      required:
        - email
        - username
        - firstName
        - lastName
        - password

    UpdateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        username:
          type: string
          description: User's chosen username
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
      # No required fields for updates - all are optional

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
        limit:
          type: integer
          description: Number of items per page
        total:
          type: integer
          description: Total number of items
        totalPages:
          type: integer
          description: Total number of pages
      required:
        - page
        - limit
        - total
        - totalPages

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []