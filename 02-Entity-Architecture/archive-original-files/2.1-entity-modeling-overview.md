# Part 2.2: Entity Modeling with Cortex

As software ecosystems evolve from simple applications into complex networks of microservices, cloud infrastructure, and third-party integrations, a simple component model is no longer sufficient. The Cortex methodology provides a comprehensive entity model designed to tame this complexity by creating a complete, queryable map of your entire software world. It helps answer not just "what code do we have?" but "who owns it?", "what does it depend on?", and "how does it provide value?"

This guide explains the philosophy behind the Cortex entity model and how to use it to represent our architecture using our platform-agnostic approach.

## The Hospital Analogy

To understand the Cortex entity model, we'll use an analogy: a hospital.

- **System:** A whole department in the hospital (e.g., **Cardiology Department**) - the highest-level business capability.
- **Feature:** A specific service that department provides (e.g., **Emergency Heart Attack Response**, **Routine Checkups**) - user-facing capabilities.
- **Component:** The specific people, tools, and software involved in delivering those features (e.g., the **Ambulance** `service`, the **Patient Intake** `website`, the **Heart Monitor** `library`).
- **API:** The standardized forms and communication protocols they use (e.g., the **Triage Checklist**, **Blood Test Request Form**).
- **Resource:** The hospital's shared utilities (e.g., the **Power Grid**, the **Central Pharmacy**, the **Patient Database**).

## The Cortex Entity Model: A Deep Dive

### `System` - The Strategic Container
This is the highest-level entity in our model. It represents a major business capability or architectural domain. In our analogy, this is the entire **Cardiology Department**. A System is a conceptual container that groups all the Features and Components that work together to deliver significant business value. It provides the strategic context and business justification for why this collection of software exists.

### `Feature` - The Bridge Between Business and Technology  
This entity represents a specific, user-facing capability within a System. It is the **Emergency Heart Attack Response** or **Routine Checkups**. A Feature acts as the crucial bridge, connecting the high-level business context of a System to the low-level technical Components that implement it. Features describe the "what" and "why" of capabilities, serving as aggregation points for their underlying components.

### `Component` - The Building Blocks
This is the fundamental entity representing a single piece of software. Components come in five distinct types, each serving different architectural roles:

#### **`Service`**: The Active Performers
A long-running application that can be called over a network. This is the **Ambulance Service** or the **EKG Monitoring Service**. It actively processes requests, manages state, and provides functionality through APIs. Services are the workhorses of your architecture.

#### **`API`**: The Communication Contracts
The formal, versioned contract for how components communicate. This is the standardized **Triage Checklist** or **Blood Test Request Form**. APIs define the interface without implementing it—they're the user manual that ensures predictable communication between services.

#### **`Library`**: The Reusable Logic
A bundle of reusable code consumed by other components. This is the **Stent Placement Protocol Library**—a set of validated procedures and utilities used across multiple services. Libraries encapsulate shared business logic and technical utilities.

#### **`Website`**: The User Interfaces
A user-facing web application. This is the **Patient Portal** or the **Doctor's Dashboard**. Websites provide the human interface to your systems, translating complex backend functionality into accessible user experiences.

#### **`Resource`**: The Infrastructure Dependencies
A piece of infrastructure that components depend on. This represents the hospital's **Power Grid**, **Central Pharmacy Database**, or **Oxygen Supply System**. Resources are not code—they're external dependencies that enable your code to function.

## The Three-File Documentation Architecture

Before diving into relationships, it's crucial to understand how Cortex structures documentation. Each entity uses a **three-file model** that serves different audiences while maintaining consistency:

### `catalog-info.yaml` - The Manifest
A lean file that identifies the entity and provides basic metadata. This serves as the entry point for automated tooling, service discovery, and dependency analysis systems.

### `soul.yaml` - The AI Fuel  
The structured, machine-readable data that contains the exhaustive details about the entity. This enables AI systems to understand the entity's complete specification, dependencies, and operational context without reading source code.

### `index.md` - The Narrative
The human-readable story that provides context, design philosophy, and usage patterns. This gives developers and architects the business context they need to understand the entity's role in the broader system.

## The Dimensional Framework

Every entity in Cortex is documented across three essential dimensions that provide complete "hyperspace coordinates" for AI navigation. This framework enables instant teleportation to perfect understanding of any aspect of an entity without traversing the entire codebase.

### **Structural Dimension**
Answers: *"What is this entity, where does it fit, and how is it built?"*

This dimension describes the static, intrinsic properties of the entity:

- **Architectural Blueprint:** The entity's location in the system hierarchy (System, Layer) and its fundamental pattern (e.g., Library, Service, Website, API, Resource)
- **Core Logic & Data:** A description of the core algorithm or logic (its "Functional DNA") and the schema of the data it owns or accesses (its "Data Model")
- **Data Flow:** Defines how information moves through the entity: inputs, key processing steps, and outputs (for both success and failure)
- **Configuration:** A comprehensive list of all environment variables, feature flags, and settings that control the entity's behavior

### **Operational Dimension**  
Answers: *"How does this entity behave and how should I interact with it?"*

This dimension covers the dynamic, observable characteristics of the entity in action:

- **Usage & Interaction:** Concrete code examples showing correct usage, common patterns, and expected testing scenarios (happy path, errors, edge cases)
- **Performance & Resilience:** Defines the entity's expected performance characteristics, including latency, throughput, known bottlenecks, and how it handles failures
- **Security Profile:** Describes the entity's security posture, including data classification, threat model, and mitigation strategies
- **Error Handling:** How the entity handles and communicates failures, including error types, formats, and recovery strategies

### **Strategic Dimension**
Answers: *"Why does this entity exist and where is it going?"*

This dimension provides the business and historical context:

- **Genesis & Business Value:** The origin story explaining the business problem that led to its creation, the value it provides, its impact on users, and the risks it mitigates
- **Design Philosophy:** The key architectural principles and trade-offs that guided the entity's implementation
- **Lifecycle Roadmap:** A log of significant past changes ("Evolution Path") and the plan for the future, including new features, scaling triggers, and potential deprecation

## Defining Entity Relationships

The power of the Cortex model lies in explicitly defining how entities relate to each other, creating a complete architectural map.

### **System → Feature Relationships**
Systems contain Features that deliver specific capabilities:
```yaml
# In a System's catalog-info.yaml
contains:
  - feature:default/user-registration
  - feature:default/user-login
  - feature:default/password-reset
```

### **Feature → Component Relationships**  
Features are implemented by Collections of Components:
```yaml
# In a Feature's catalog-info.yaml
implementedBy:
  - component:default/auth-service
  - component:default/session-library
  - component:default/login-form-ui
```

### **Component Dependencies**
Components depend on other Components and Resources:
```yaml
# In a Service Component's catalog-info.yaml  
dependsOn:
  - component:default/auth-validators
  - resource:default/postgres-auth
```

### **API Contracts**
Services provide APIs, while other Components consume them:
```yaml
# Service providing an API
providesApis:
  - api:default/auth-api

# Component consuming an API  
consumesApis:
  - api:default/auth-api
```

## AI Context Integration

A crucial innovation in the Cortex methodology is the **AI Context Header** found in each entity's documentation. This structured metadata enables AI systems to instantly understand an entity's identity, relationships, and operational constraints without deep code analysis.

### **The Machine Interface**
Each entity includes a JSON structure that provides:
- **Entity Identity**: Kind, name, owner, and lifecycle stage
- **Contracts**: API specifications, dependencies, and integration points  
- **Flows**: Step-by-step business logic and operational workflows
- **Rules**: Non-negotiable constraints and business policies
- **Dependencies**: Critical components and their failure impact levels

### **Hyperspace Navigation**
This structured approach enables what we call "hyperspace navigation"—AI systems can instantly teleport to complete understanding of any entity without linearly reading source code. The AI Context Header serves as dimensional coordinates, providing immediate access to:
- Purpose and business value
- Technical implementation patterns  
- Integration requirements
- Operational constraints
- Performance expectations

## Practical Implementation Example

Let's see how this works in practice with a User Authentication System:

### **System Level: User Authentication**
```yaml
# user-authentication-system/catalog-info.yaml
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: user-authentication
  description: "Centralized user identity and access management"
spec:
  owner: platform-team
  domain: identity-management
```

**Contains Features:**
- User Registration (`feature:default/user-registration`)
- User Login (`feature:default/user-login`)  
- Password Management (`feature:default/password-reset`)

### **Feature Level: User Login**
```yaml
# user-login-feature/catalog-info.yaml
apiVersion: backstage.io/v1alpha1  
kind: Feature
metadata:
  name: user-login
  description: "Secure user authentication capability"
spec:
  owner: product-team
  partOf: system:default/user-authentication
```

**Implemented By Components:**
- Authentication Service (`component:default/auth-service`)
- Session Library (`component:default/session-library`)
- Login UI (`component:default/login-form`)

### **Component Level: Authentication Service**
```yaml
# auth-service/catalog-info.yaml
apiVersion: backstage.io/v1alpha1
kind: Component  
metadata:
  name: auth-service
  description: "Core authentication and session management service"
spec:
  type: service
  owner: backend-team
  system: user-authentication
  dependsOn:
    - component:default/auth-validators
    - resource:default/user-database
  providesApis:
    - api:default/auth-api
```

This hierarchical structure creates a complete map from business capability (System) through user value (Feature) to technical implementation (Components), with each level providing the appropriate level of detail for its audience.

## The Power of Platform-Agnostic Modeling

By structuring our architecture this way, we gain several critical advantages:

1. **Technology Independence**: The model describes relationships and capabilities independent of any specific platform or tooling
2. **Complete Traceability**: From business requirement to running code, every connection is explicit
3. **AI-First Documentation**: Machine-readable context enables intelligent automation and decision-making  
4. **Evolutionary Architecture**: Clear separation of concerns allows components to evolve independently while maintaining system coherence
5. **Onboarding Acceleration**: New team members can understand both business context and technical implementation through a single, consistent model

The Cortex entity model transforms software architecture from implicit tribal knowledge into an explicit, navigable, and AI-enhanced system that serves both human understanding and machine automation.