# 3. Maintain Hierarchy Integrity

#### What This Means: Understanding Organizational Structure in Software

Imagine you're looking at a company's organizational chart. At the top, you have major divisions like "Sales Department" and "Engineering Department." Within each division, you have specific teams like "Enterprise Sales Team" and "Customer Support Team." And within each team, you have individual people with specific roles like "Senior Sales Representative" and "Customer Success Manager."

This hierarchy makes sense because:
- **Divisions** group related business functions
- **Teams** deliver specific capabilities within those functions  
- **People** perform the actual work to make those capabilities happen

Now imagine if the organizational chart was chaotic: individual salespeople reporting directly to the CEO, customer support people scattered across different divisions, or entire teams trying to report to other teams instead of managers. The company would be impossible to manage, understand, or navigate.

Software architecture works exactly the same way. **Hierarchy integrity** means organizing your software components in a logical, consistent structure that mirrors how businesses actually work. Just like a well-organized company, a well-organized software system makes it easy for anyone to understand what goes where and why.

#### The Company Organizational Chart Analogy

Let's use a hospital system to understand proper software hierarchy:

**🏥 Hospital Organizational Structure (Good Hierarchy)**
```
Riverside General Hospital (The Organization)
├── Cardiology Department (Major Division)
│   ├── Emergency Heart Care (Specific Service)
│   │   ├── Dr. Smith - Cardiologist (Individual Person)
│   │   ├── Nurse Johnson - ICU Specialist (Individual Person)
│   │   └── Heart Monitor Equipment (Tools/Resources)
│   └── Routine Heart Checkups (Specific Service)
│       ├── Dr. Brown - Cardiologist (Individual Person)
│       ├── Nurse Davis - Clinic Specialist (Individual Person)
│       └── EKG Machine (Tools/Resources)
└── Emergency Department (Major Division)
    ├── Trauma Response (Specific Service)
    │   ├── Dr. Wilson - Trauma Surgeon (Individual Person)
    │   ├── Nurse Martinez - Trauma Specialist (Individual Person)
    │   └── Surgical Equipment (Tools/Resources)
    └── General Emergency Care (Specific Service)
        ├── Dr. Lee - Emergency Physician (Individual Person)
        ├── Nurse Thompson - ER Specialist (Individual Person)
        └── Diagnostic Equipment (Tools/Resources)
```

**🏥 Software Architecture Hierarchy (Mirrors Hospital Structure)**
```
Patient Care System (System - like Hospital Organization)
├── Cardiology Management (Feature - like Department Service)
│   ├── heart-monitoring-service (Component - like Individual Doctor)
│   ├── cardiac-alert-library (Component - like Individual Nurse)
│   └── heart-monitor-database (Resource - like Equipment)
└── Emergency Response (Feature - like Department Service)
    ├── triage-service (Component - like Individual Doctor)
    ├── emergency-notification-library (Component - like Individual Nurse)
    └── patient-tracking-database (Resource - like Equipment)
```

#### Why Systems Contain Features, Not Components Directly

This is one of the most important hierarchy rules, and it's easy to understand with our hospital analogy:

**❌ Wrong Hospital Structure (Chaos)**
```
Riverside General Hospital
├── Dr. Smith (Individual person reporting to CEO)
├── Nurse Johnson (Individual person reporting to CEO)
├── Dr. Brown (Individual person reporting to CEO)
├── Heart Monitor (Equipment reporting to CEO)
├── EKG Machine (Equipment reporting to CEO)
└── ... (50 more individual people and equipment)
```

**Problems with this structure:**
- The CEO can't manage 50+ individual people directly
- No clear understanding of what services the hospital provides
- Impossible to coordinate care between related specialists
- New patients don't know which doctor handles their condition
- No way to organize resources or plan capacity

**✅ Correct Hospital Structure (Clear Organization)**
```
Riverside General Hospital
├── Cardiology Department (Groups related heart care)
│   ├── Emergency Heart Care (Specific service patients can access)
│   └── Routine Heart Checkups (Specific service patients can access)
└── Emergency Department (Groups related emergency care)
    ├── Trauma Response (Specific service patients can access)
    └── General Emergency Care (Specific service patients can access)
```

**Benefits of this structure:**
- Clear services that patients can understand and access
- Related specialists work together in departments
- Easy to find the right care for any condition
- Manageable organization with clear responsibilities

**🏥 Software Hierarchy (Same Logic)**

**❌ Wrong Software Structure (System directly contains Components)**
```yaml
# DON'T DO THIS - System containing Components directly
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: patient-care-system
spec:
  contains:
    - component:default/heart-monitoring-service      # Individual component
    - component:default/cardiac-alert-library         # Individual component  
    - component:default/triage-service               # Individual component
    - component:default/emergency-notification-library # Individual component
    - component:default/patient-tracking-service     # Individual component
    # ... 20 more individual components
```

**Problems with this structure:**
- No clear understanding of what business capabilities the system provides
- Impossible to see which components work together
- New developers can't understand what the system does
- No way to organize or plan feature development
- Components from different business areas mixed together

**✅ Correct Software Structure (System contains Features, Features contain Components)**
```yaml
# System defines the major business capability
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: patient-care-system
  description: Comprehensive hospital patient management and care coordination
spec:
  contains:
    - feature:default/cardiology-management          # Business capability
    - feature:default/emergency-response            # Business capability
    - feature:default/patient-registration          # Business capability

---
# Feature defines specific user-facing capability
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: cardiology-management
spec:
  type: feature
  implementedBy:
    - component:default/heart-monitoring-service     # Technical implementation
    - component:default/cardiac-alert-library        # Technical implementation
    - component:default/cardiology-ui               # Technical implementation

---
# Feature defines specific user-facing capability  
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: emergency-response
spec:
  type: feature
  implementedBy:
    - component:default/triage-service              # Technical implementation
    - component:default/emergency-notification-library # Technical implementation
    - component:default/emergency-ui               # Technical implementation
```

**Benefits of this structure:**
- Clear business capabilities that users can understand
- Technical components grouped by the features they support
- Easy to understand what the system does and how it's organized
- New developers can navigate from business need to technical implementation

#### Examples of Correct and Incorrect Hierarchy Arrangements

Let's look at several real-world examples to solidify your understanding:

##### Example 1: E-Commerce Platform

**❌ Incorrect Hierarchy (Mixed Levels)**
```
E-Commerce Platform (System)
├── Product Catalog (Feature) ✓ Correct
├── shopping-cart-service (Component) ❌ Wrong - Component at System level
├── User Authentication (Feature) ✓ Correct  
├── postgres-database (Resource) ❌ Wrong - Resource at System level
└── Order Processing (Feature) ✓ Correct
    ├── Inventory Management (Feature) ❌ Wrong - Feature inside Feature
    └── payment-service (Component) ✓ Correct
```

**Problems:**
- Components and Resources mixed with Features at System level
- Feature inside another Feature creates confusion
- No clear separation between business capabilities and technical implementation

**✅ Correct Hierarchy (Clean Organization)**
```
E-Commerce Platform (System)
├── Product Catalog (Feature)
│   ├── catalog-service (Component)
│   ├── search-library (Component)
│   └── product-database (Resource)
├── Shopping Cart (Feature)
│   ├── cart-service (Component)
│   ├── cart-ui-component (Component)
│   └── cart-session-cache (Resource)
├── User Authentication (Feature)
│   ├── auth-service (Component)
│   ├── login-ui-component (Component)
│   └── user-database (Resource)
└── Order Processing (Feature)
    ├── order-service (Component)
    ├── payment-service (Component)
    ├── inventory-service (Component)
    └── order-database (Resource)
```

**Why this works:**
- System contains only Features (business capabilities)
- Each Feature contains only Components and Resources (technical implementation)
- Clear separation between what the system does (Features) and how it does it (Components)
- Easy to understand both business value and technical architecture

##### Example 2: Content Management System

**❌ Incorrect Hierarchy (Confusing Structure)**
```
Content Management System (System)
├── content-api (Component) ❌ Wrong - Component at System level
├── Article Publishing (Feature) ✓ Correct
│   ├── Media Management (Feature) ❌ Wrong - Feature inside Feature
│   └── editor-ui (Component) ✓ Correct
├── User Management (Feature) ✓ Correct
└── cdn-storage (Resource) ❌ Wrong - Resource at System level
```

**✅ Correct Hierarchy (Clear Structure)**
```
Content Management System (System)
├── Article Publishing (Feature)
│   ├── content-service (Component)
│   ├── editor-ui (Component)
│   ├── publishing-workflow-library (Component)
│   └── content-database (Resource)
├── Media Management (Feature)
│   ├── media-upload-service (Component)
│   ├── image-processing-library (Component)
│   ├── media-gallery-ui (Component)
│   └── cdn-storage (Resource)
└── User Management (Feature)
    ├── user-service (Component)
    ├── permission-library (Component)
    ├── admin-ui (Component)
    └── user-database (Resource)
```

##### Example 3: Financial Trading Platform

**❌ Incorrect Hierarchy (Business and Technical Mixed)**
```
Trading Platform (System)
├── Market Data (Feature) ✓ Correct
├── Trade Execution (Feature) ✓ Correct
├── kafka-message-bus (Resource) ❌ Wrong - Resource at System level
├── Portfolio Management (Feature) ✓ Correct
│   ├── Risk Assessment (Feature) ❌ Wrong - Feature inside Feature
│   └── portfolio-service (Component) ✓ Correct
└── market-data-service (Component) ❌ Wrong - Component at System level
```

**✅ Correct Hierarchy (Proper Business-Technical Separation)**
```
Trading Platform (System)
├── Market Data (Feature)
│   ├── market-data-service (Component)
│   ├── price-feed-library (Component)
│   ├── market-data-ui (Component)
│   └── market-database (Resource)
├── Trade Execution (Feature)
│   ├── trading-service (Component)
│   ├── order-matching-library (Component)
│   ├── trading-ui (Component)
│   └── trade-database (Resource)
├── Portfolio Management (Feature)
│   ├── portfolio-service (Component)
│   ├── portfolio-ui (Component)
│   └── portfolio-database (Resource)
└── Risk Assessment (Feature)
    ├── risk-calculation-service (Component)
    ├── risk-monitoring-library (Component)
    ├── risk-dashboard-ui (Component)
    └── risk-metrics-database (Resource)
```

**Key Changes Made:**
1. **Risk Assessment** became its own Feature instead of being nested inside Portfolio Management
2. **kafka-message-bus** moved to be a Resource under the Features that use it
3. **market-data-service** moved under the Market Data Feature
4. Clear separation: System → Features → Components/Resources

#### Understanding the "Why" Behind Hierarchy Rules

The hierarchy rules aren't arbitrary—they reflect how businesses actually work and how people think about complex systems:

##### Rule 1: Systems Contain Features (Not Components)

**Business Reason**: When someone asks "What does this system do?", they want to know about business capabilities, not technical implementation details.

**Hospital Example**: 
- ✅ "Our hospital provides Cardiology Care, Emergency Services, and Surgery"
- ❌ "Our hospital has Dr. Smith, Nurse Johnson, and an EKG machine"

**Software Example**:
- ✅ "Our e-commerce system provides Product Catalog, Shopping Cart, and Order Processing"
- ❌ "Our e-commerce system has catalog-service, cart-database, and payment-api"

##### Rule 2: Features Are Implemented By Components (Not Other Features)

**Business Reason**: Features represent complete user-facing capabilities. They don't contain other complete capabilities—they're implemented by technical components working together.

**Hospital Example**:
- ✅ "Emergency Heart Care is provided by cardiologists, nurses, and equipment working together"
- ❌ "Emergency Heart Care contains Routine Checkups" (these are separate services)

**Software Example**:
- ✅ "Shopping Cart is implemented by cart-service, cart-ui, and cart-database working together"
- ❌ "Shopping Cart contains Product Recommendations" (these are separate features)

##### Rule 3: Components Depend On Resources (Not Other Components Directly When Possible)

**Business Reason**: Resources represent shared infrastructure that multiple components can use. Direct component-to-component dependencies create tight coupling.

**Hospital Example**:
- ✅ "Doctors use shared equipment and facilities"
- ❌ "Dr. Smith is permanently attached to Dr. Brown" (they should be independent)

**Software Example**:
- ✅ "Services use shared databases and message queues"
- ❌ "Every service is hardwired to every other service" (creates maintenance nightmares)

#### The Organizational Chart Mental Model

When designing your software hierarchy, think like you're designing a company organizational chart:

**Questions to Ask Yourself:**

1. **At the System Level**: "What major business divisions does this organization have?"
   - Example: Sales Division, Engineering Division, Customer Support Division

2. **At the Feature Level**: "What specific services does each division provide to customers?"
   - Example: Enterprise Sales, Product Development, Technical Support

3. **At the Component Level**: "What specific roles and tools are needed to deliver each service?"
   - Example: Sales Representatives, Software Engineers, Support Specialists

4. **At the Resource Level**: "What shared infrastructure do these roles depend on?"
   - Example: Office Buildings, Computer Networks, Phone Systems

**Applying This to Software:**

1. **System**: "What major business capability does this software provide?"
   - Example: Customer Relationship Management, E-Commerce Platform, Financial Trading

2. **Feature**: "What specific user-facing capabilities does this system offer?"
   - Example: Contact Management, Product Catalog, Trade Execution

3. **Component**: "What specific software pieces work together to deliver each capability?"
   - Example: contact-service, catalog-ui, trading-engine

4. **Resource**: "What shared infrastructure do these components depend on?"
   - Example: customer-database, product-images-storage, market-data-feed

This mental model helps you create hierarchies that make intuitive sense to both business stakeholders and technical teams.

#### Common Hierarchy Mistakes and Why They Happen

Understanding why people make hierarchy mistakes helps you avoid them:

##### Mistake 1: "I'll just put everything at the System level"

**Why People Do This**: It seems simpler to have a flat structure.

**Hospital Analogy**: Like having all doctors, nurses, and equipment report directly to the hospital CEO.

**Problems**:
- CEO (System) can't manage 100+ direct reports
- No clear understanding of what services the hospital provides
- Impossible to coordinate related specialists
- New patients don't know where to go for specific care

**Software Problems**:
- System purpose becomes unclear
- No grouping of related functionality
- Difficult to understand what the system does
- Hard to plan feature development or assign ownership

##### Mistake 2: "I'll nest Features inside other Features"

**Why People Do This**: Some capabilities seem related, so they try to show that relationship through nesting.

**Hospital Analogy**: Like putting "Routine Checkups" inside "Emergency Heart Care" because they're both heart-related.

**Problems**:
- Patients can't access Routine Checkups during emergencies
- Emergency staff aren't trained for routine care
- Scheduling and resources get confused
- Each service has different requirements and workflows

**Software Problems**:
- Features become unclear in scope and ownership
- Users can't understand what capabilities are available
- Development teams don't know what they're responsible for
- Feature boundaries become blurred and unmaintainable

##### Mistake 3: "I'll put Resources at the System level because they're shared"

**Why People Do This**: Resources are used by multiple components, so it seems logical to put them at the top level.

**Hospital Analogy**: Like having "Oxygen Supply" report directly to the CEO instead of being managed by the departments that use it.

**Problems**:
- CEO doesn't understand oxygen supply requirements
- Departments can't plan their oxygen needs
- No clear ownership of oxygen supply maintenance
- Difficult to coordinate oxygen supply with patient care

**Software Problems**:
- No clear ownership of resource management
- Difficult to understand which Features depend on which Resources
- Resource planning becomes disconnected from business needs
- Harder to track resource usage and costs

#### Quick Reference: Hierarchy Rules

**✅ Correct Hierarchy Pattern:**
```
System (Business Capability)
├── Feature (User-Facing Service)
│   ├── Component (Technical Implementation)
│   ├── Component (Technical Implementation)
│   └── Resource (Infrastructure Dependency)
├── Feature (User-Facing Service)
│   ├── Component (Technical Implementation)
│   └── Resource (Infrastructure Dependency)
└── Feature (User-Facing Service)
    ├── Component (Technical Implementation)
    ├── Component (Technical Implementation)
    └── Resource (Infrastructure Dependency)
```

**❌ Common Mistakes to Avoid:**
```
System
├── Component ❌ (Components don't belong at System level)
├── Resource ❌ (Resources don't belong at System level)
├── Feature ✅ (Correct)
│   ├── Feature ❌ (Features don't contain other Features)
│   └── Component ✅ (Correct)
└── Feature ✅ (Correct)
```

**Memory Aid - The "Company Rule":**
- **System** = Company (overall business)
- **Feature** = Department (specific business function)
- **Component** = Employee (specific role/job)
- **Resource** = Equipment (tools employees use)

Just like employees don't report to the company CEO directly (they report to department managers), Components don't belong directly under Systems (they belong under Features).

#### Decision Framework for Proper Component Placement

When you're organizing your software architecture, you need a systematic way to decide where each piece belongs. Just like a company HR department has clear rules for where new employees fit in the organizational chart, you need clear rules for where software components belong in your system hierarchy.

##### The "Where Does This Belong?" Decision Tree

Use this step-by-step decision process whenever you're unsure about where to place a software entity:

```
🤔 I have a software entity to place. Where does it belong?

┌─ STEP 1: What type of entity is this?
│
├─ 🏢 Is this a major business capability or domain?
│  └─ YES → This is a SYSTEM
│     └─ Place at: Top level of your architecture
│
├─ 🎯 Is this a specific user-facing capability within a business domain?
│  └─ YES → This is a FEATURE  
│     └─ Place at: Inside the relevant System
│
├─ ⚙️ Is this a piece of software that implements functionality?
│  └─ YES → This is a COMPONENT
│     └─ Go to STEP 2 (Component Placement)
│
└─ 🔧 Is this infrastructure that software depends on?
   └─ YES → This is a RESOURCE
      └─ Go to STEP 3 (Resource Placement)

┌─ STEP 2: Component Placement
│  "Which Feature does this Component help implement?"
│
├─ 🎯 Does this Component directly serve end users for a specific capability?
│  └─ YES → Place under the Feature that provides that capability
│
├─ 📚 Does this Component provide reusable logic used by multiple Features?
│  └─ YES → Place under the Feature that most heavily uses it
│     └─ Note: Document the sharing relationship in other Features
│
└─ 🤷 Does this Component serve multiple Features equally?
   └─ Consider: Should this be multiple Components, one per Feature?
      └─ Or: Create a new "Shared Services" Feature if truly cross-cutting

┌─ STEP 3: Resource Placement  
│  "Which Components directly depend on this Resource?"
│
├─ 🎯 Does only one Feature's Components use this Resource?
│  └─ YES → Place under that Feature
│
├─ 🔄 Do multiple Features' Components use this Resource?
│  └─ YES → Place under the Feature that owns/manages the Resource
│     └─ Document dependencies from other Features
│
└─ 🤷 Is this Resource used equally by all Features?
   └─ Place under the most infrastructure-focused Feature
      └─ Or: Create a "Platform Services" Feature for shared infrastructure
```

##### Practical "Where Does This Belong?" Guide with Examples

Let's work through real examples to show how the decision tree works in practice:

**Example 1: You have a "user-authentication-service"**

🤔 **Question**: Where does this belong?

**Step 1**: What type of entity is this?
- ❌ Not a major business capability (that would be something like "Customer Management System")
- ❌ Not a user-facing capability (users don't directly interact with authentication as a standalone feature)
- ✅ **This is a COMPONENT** (piece of software that implements functionality)

**Step 2**: Component Placement - Which Feature does this help implement?
- 🎯 Does this serve end users for a specific capability? 
  - ✅ **YES** - Users need to log in to access the system
  - This belongs under a **"User Authentication"** Feature

**Final Placement**:
```yaml
Customer Management System (System)
└── User Authentication (Feature)
    └── user-authentication-service (Component) ← HERE
```

**Example 2: You have a "postgres-customer-database"**

🤔 **Question**: Where does this belong?

**Step 1**: What type of entity is this?
- ❌ Not a major business capability
- ❌ Not a user-facing capability  
- ❌ Not software that implements functionality
- ✅ **This is a RESOURCE** (infrastructure that software depends on)

**Step 3**: Resource Placement - Which Components depend on this?
- Components: customer-service, customer-ui, customer-reporting-service
- All these Components belong to the "Customer Management" Feature
- ✅ **Only one Feature uses this Resource**

**Final Placement**:
```yaml
Customer Management System (System)
└── Customer Management (Feature)
    ├── customer-service (Component)
    ├── customer-ui (Component)  
    ├── customer-reporting-service (Component)
    └── postgres-customer-database (Resource) ← HERE
```

**Example 3: You have "Product Recommendations"**

🤔 **Question**: Where does this belong?

**Step 1**: What type of entity is this?
- ❌ Not a major business capability (that would be "E-Commerce Platform")
- 🎯 Is this a specific user-facing capability? 
  - ✅ **YES** - Users see and interact with product recommendations
  - **This is a FEATURE**

**Feature Placement**: Which System does this belong to?
- This provides e-commerce functionality
- Belongs under "E-Commerce Platform" System

**Final Placement**:
```yaml
E-Commerce Platform (System)
├── Product Catalog (Feature)
├── Shopping Cart (Feature)
├── Product Recommendations (Feature) ← HERE
└── Order Processing (Feature)
```

**Example 4: You have a "validation-library"**

🤔 **Question**: Where does this belong?

**Step 1**: What type of entity is this?
- ✅ **This is a COMPONENT** (software that implements functionality)

**Step 2**: Component Placement - Which Feature does this help implement?
- 📚 Does this provide reusable logic used by multiple Features?
  - ✅ **YES** - Used by User Registration, Product Creation, and Order Processing
  - Place under the Feature that most heavily uses it
  - Let's say "User Registration" uses it most heavily

**Final Placement**:
```yaml
E-Commerce Platform (System)
├── User Registration (Feature)
│   ├── registration-service (Component)
│   ├── registration-ui (Component)
│   └── validation-library (Component) ← HERE (primary location)
├── Product Management (Feature)
│   ├── product-service (Component)
│   └── dependsOn: validation-library (documented dependency)
└── Order Processing (Feature)
    ├── order-service (Component)
    └── dependsOn: validation-library (documented dependency)
```

This decision framework gives you a systematic approach to organizing your software architecture in a way that makes sense to both business stakeholders and technical teams. Remember: when in doubt, think about how you'd organize a company's departments and employees—the same logic applies to software systems.
