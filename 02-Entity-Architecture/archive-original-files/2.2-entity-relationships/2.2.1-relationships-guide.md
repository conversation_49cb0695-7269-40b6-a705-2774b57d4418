# Part 2.1: Entity Model & Relationships

> **Purpose:** This document explains the Cortex entity model philosophy and how different entity types relate to each other. It provides the conceptual foundation for understanding how to model your software architecture.

## Table of Contents

- [The Hospital Analogy](#the-hospital-analogy)
- [The Entity Hierarchy](#the-entity-hierarchy)
  - [Top-Level Business Entities](#top-level-business-entities)
    - [System - The Strategic Container](#system---the-strategic-container)
    - [Feature - The Business-Technology Bridge](#feature---the-business-technology-bridge)
  - [Technical Implementation Entities](#technical-implementation-entities)
    - [Component - The Building Blocks](#component---the-building-blocks)
      - [Service - The Active Performers](#service---the-active-performers)
      - [API - The Communication Contracts](#api---the-communication-contracts)
      - [Library - The Reusable Logic](#library---the-reusable-logic)
      - [Website - The User Interfaces](#website---the-user-interfaces)
      - [Resource - The Infrastructure Dependencies](#resource---the-infrastructure-dependencies)
- [Entity Relationship Patterns](#entity-relationship-patterns)
  - [Hierarchical Relationships](#hierarchical-relationships)
  - [Relationship Types](#relationship-types)
    - [Containment Relationships](#containment-relationships)
    - [Implementation Relationships](#implementation-relationships)
    - [Dependency Relationships](#dependency-relationships)
    - [Contract Relationships](#contract-relationships)
- [Relationship Best Practices](#relationship-best-practices)
  - [Why Relationships Matter: The Foundation of Maintainable Software](#why-relationships-matter-the-foundation-of-maintainable-software)
  - [1. Keep Relationships Explicit](#1-keep-relationships-explicit)
  - [2. Minimize Coupling](#2-minimize-coupling)
  - [3. Maintain Hierarchy Integrity](#3-maintain-hierarchy-integrity)
  - [4. Version Your Contracts](#4-version-your-contracts)
- [Interactive Learning Exercises](#interactive-learning-exercises)
- [Quick Reference Cards](#quick-reference-cards)
- [Common Patterns](#common-patterns)
  - [The Microservices Pattern](#the-microservices-pattern)
  - [The Monolithic Pattern](#the-monolithic-pattern)
  - [The Library-Centric Pattern](#the-library-centric-pattern)
- [Entity Lifecycle Considerations](#entity-lifecycle-considerations)
  - [Creation Guidelines](#creation-guidelines)
  - [Evolution Patterns](#evolution-patterns)
  - [Deprecation Strategies](#deprecation-strategies)
- [Platform-Agnostic Benefits](#platform-agnostic-benefits)
- [Next Steps](#next-steps)

## The Hospital Analogy

To understand the Cortex entity model, imagine a hospital system:

- **System:** A whole department (e.g., **Cardiology Department**) - the highest-level business capability
- **Feature:** Specific services that department provides (e.g., **Emergency Heart Attack Response**, **Routine Checkups**) - user-facing capabilities
- **Component:** The specific people, tools, and software involved (e.g., **Ambulance** service, **Patient Intake** website, **Heart Monitor** library)
- **API:** Standardized communication protocols (e.g., **Triage Checklist**, **Blood Test Request Form**)
- **Resource:** Shared infrastructure (e.g., **Power Grid**, **Central Pharmacy**, **Patient Database**)

## The Entity Hierarchy

### Top-Level Business Entities

#### System - The Strategic Container
**What it represents:** A major business capability or architectural domain  
**Hospital analogy:** The entire Cardiology Department  
**Purpose:** Groups all Features and Components that deliver significant business value  

A System provides:
- Strategic context for why this collection of software exists
- Business justification and value proposition
- Organizational boundaries and ownership

#### Feature - The Business-Technology Bridge
**What it represents:** A specific, user-facing capability within a System  
**Hospital analogy:** Emergency Heart Attack Response or Routine Checkups  
**Purpose:** Connects high-level business context to technical implementation  

A Feature acts as:
- The "what" and "why" of capabilities
- Aggregation point for underlying components
- User value delivery mechanism

### Technical Implementation Entities

#### Component - The Building Blocks
**What it represents:** A single piece of software with a specific technical role  
**Purpose:** The fundamental units that implement Features  

Components come in five types, each serving different architectural roles:

##### Service - The Active Performers
- **Definition:** Long-running applications callable over a network
- **Hospital analogy:** Ambulance Service, EKG Monitoring Service
- **Characteristics:**
  - Actively processes requests
  - Manages state
  - Provides functionality through APIs
  - The workhorses of your architecture

##### API - The Communication Contracts
- **Definition:** Formal, versioned contracts for component communication
- **Hospital analogy:** Standardized Triage Checklist, Blood Test Request Form
- **Characteristics:**
  - Defines interfaces without implementation
  - Ensures predictable communication
  - Acts as the "user manual" between services

##### Library - The Reusable Logic
- **Definition:** Bundles of reusable code consumed by other components
- **Hospital analogy:** Stent Placement Protocol Library
- **Characteristics:**
  - Encapsulates shared business logic
  - Provides technical utilities
  - Promotes code reuse and consistency

##### Website - The User Interfaces
- **Definition:** User-facing web applications
- **Hospital analogy:** Patient Portal, Doctor's Dashboard
- **Characteristics:**
  - Provides human interface to systems
  - Translates backend functionality to user experiences
  - Focuses on usability and accessibility

##### Resource - The Infrastructure Dependencies
- **Definition:** Infrastructure pieces that components depend on
- **Hospital analogy:** Power Grid, Central Pharmacy Database, Oxygen Supply
- **Characteristics:**
  - Not code, but external dependencies
  - Enables code to function
  - Requires operational management

## Entity Relationship Patterns

### Hierarchical Relationships

```
System
  └── contains → Features
        └── implementedBy → Components
              └── dependsOn → Resources
```

### Relationship Types

#### Containment Relationships
**System → Feature**
```yaml
# In System's catalog-info.yaml
spec:
  contains:
    - feature:default/user-registration
    - feature:default/user-login
    - feature:default/password-reset
```

#### Implementation Relationships
**Feature → Component**
```yaml
# In Feature's catalog-info.yaml
spec:
  implementedBy:
    - component:default/auth-service
    - component:default/session-library
    - component:default/login-form-ui
```

#### Dependency Relationships
**Component → Component/Resource**
```yaml
# In Component's catalog-info.yaml
spec:
  dependsOn:
    - component:default/auth-validators
    - resource:default/postgres-auth
```

#### Contract Relationships
**Service ↔ API ↔ Component**
```yaml
# Service providing an API
spec:
  providesApis:
    - api:default/auth-api

# Component consuming an API
spec:
  consumesApis:
    - api:default/auth-api
```

## Relationship Best Practices

### Why Relationships Matter: The Foundation of Maintainable Software

Imagine you're new to a city and need to find your way around. You could wander randomly, asking strangers for directions each time you get lost. Or you could use a map that clearly shows how streets connect, where the landmarks are, and the best routes between destinations. Software relationships work the same way—they're the "map" that helps everyone understand how your system's pieces connect and work together.

When relationships between software components are unclear or undocumented, teams face the same problems as someone navigating without a map:
- **Getting Lost:** Developers waste time trying to understand how pieces fit together
- **Breaking Things:** Changes in one area unexpectedly break something else
- **Duplicate Work:** Teams build the same functionality because they don't know it already exists
- **Slow Onboarding:** New team members take months to understand the system instead of days

Think of proper relationship documentation like having a well-organized hospital. In a good hospital, everyone knows:
- Which departments handle what conditions (clear boundaries)
- How to transfer patients between departments (explicit handoffs)
- What equipment each department needs (documented dependencies)
- How departments communicate during emergencies (defined interfaces)

Without this clarity, patients get lost, treatments are delayed, and the whole system becomes chaotic.

#### What You'll Learn in This Section

By the end of this section, you'll understand:

1. **How to make relationships visible** - Like labeling all the connections in a building's blueprint
2. **Why loose connections are better** - Like designing modular furniture that's easy to rearrange
3. **How to organize components properly** - Like maintaining a clear company org chart
4. **How to manage changes safely** - Like updating contracts without breaking existing agreements

#### Learning Objectives

After working through these practices, you'll be able to:
- ✅ Document all dependencies between your software components
- ✅ Identify when components are too tightly connected and know how to fix it
- ✅ Organize system components in a logical hierarchy
- ✅ Plan API changes without breaking existing users
- ✅ Troubleshoot common relationship problems in software architecture
- ✅ Use relationship documentation to speed up development and onboarding

#### Section Overview

We'll cover four essential practices that transform chaotic software into well-organized, maintainable systems:

**Practice 1: Keep Relationships Explicit** - Learn to document every connection between components, just like a building blueprint shows every pipe and wire.

**Practice 2: Minimize Coupling** - Discover how to keep components loosely connected, like LEGO blocks that snap together easily but can be separated when needed.

**Practice 3: Maintain Hierarchy Integrity** - Master the art of organizing components in clear hierarchies, like a well-structured company org chart.

**Practice 4: Version Your Contracts** - Understand how to evolve APIs safely, like updating legal contracts without invalidating existing agreements.

Each practice includes real-world analogies, step-by-step implementation guidance, common mistakes to avoid, and hands-on exercises to reinforce your learning.

---

### 1. Keep Relationships Explicit

[Learn how to keep relationships explicit](./2.2.2-keep-relationships-explicit.md)

### 2. Minimize Coupling

[Learn how to minimize coupling](./2.2.3-minimize-coupling.md)

### 3. Maintain Hierarchy Integrity

[Learn how to maintain hierarchy integrity](./2.2.4-maintain-hierarchy-integrity.md)

### 4. Version Your Contracts

[Learn how to version your contracts](./2.2.5-version-your-contracts.md)

---

## Interactive Learning Exercises

For hands-on practice with these relationship best practices, refer to the dedicated [Interactive Exercises file](./interactive-exercises.md). This comprehensive resource contains detailed scenarios across all difficulty levels, complete solutions with alternative approaches, self-assessment questions, learning reinforcement activities, and advanced discussion topics to deepen your understanding and application of these concepts.

## Quick Reference Cards

For comprehensive quick reference materials, decision trees, assessment tools, and troubleshooting guides, see the dedicated [Quick Reference Cards file](./quick-reference-cards.md).

This file contains:
- 🎯 Relationship Types Quick Reference
- 🔗 Explicit Relationships Checklist  
- 🔄 Coupling Assessment Quick Card
- 🏗️ Hierarchy Rules Quick Reference
- 🚀 API Versioning Quick Decision Guide
- 📋 Common Pitfalls Prevention Checklist
- 🛠️ Emergency Troubleshooting Cards
- 📚 Learning Path Quick Reference
- Plus additional decision trees, assessment scorecards, and success metrics

Remember: These practices transform chaotic software into well-organized, maintainable systems. Start with explicit relationships, then work on reducing coupling, organizing hierarchy, and managing API versions. Each improvement makes the next one easier!

## Common Patterns

### The Microservices Pattern
```
System: E-Commerce Platform
├── Feature: Product Catalog
│   ├── Service: catalog-service
│   ├── API: catalog-api
│   └── Resource: product-database
├── Feature: Shopping Cart
│   ├── Service: cart-service
│   ├── Library: cart-calculations
│   └── Resource: redis-cache
└── Feature: Checkout
    ├── Service: checkout-service
    ├── Website: checkout-ui
    └── API: payment-api
```

### The Monolithic Pattern
```
System: Legacy Application
├── Feature: User Management
│   └── Service: monolith-app (partial)
├── Feature: Reporting
│   └── Service: monolith-app (partial)
└── Shared Resources
    ├── Resource: main-database
    └── Resource: file-storage
```

### The Library-Centric Pattern
```
System: Data Processing Platform
├── Feature: Data Ingestion
│   ├── Service: ingestion-service
│   └── Library: data-validators
├── Feature: Data Transformation
│   ├── Service: transform-service
│   ├── Library: transform-rules
│   └── Library: data-validators (shared)
└── Feature: Data Export
    ├── Service: export-service
    └── Library: format-converters
```

## Entity Lifecycle Considerations

### Creation Guidelines
- Start with Systems and Features (business value)
- Add Components as implementation details
- Define APIs before implementing services
- Document Resources before depending on them

### Evolution Patterns
- Features may split as they grow complex
- Services may decompose into microservices
- Libraries may graduate to services
- Resources may consolidate or distribute

### Deprecation Strategies
- Mark entities as deprecated in lifecycle field
- Document migration paths for dependents
- Maintain deprecated entities until all consumers migrate
- Archive rather than delete for historical reference

## Platform-Agnostic Benefits

This entity model provides several critical advantages:

1. **Technology Independence:** Describes relationships independent of specific platforms
2. **Complete Traceability:** Every connection from business to code is explicit
3. **Impact Analysis:** Changes can be traced through the relationship graph
4. **Onboarding Acceleration:** New team members understand both business and technical context
5. **AI Navigation:** Machine-readable relationships enable intelligent automation

## Next Steps

Now that you understand the entity model and relationships:

1. **Learn the Documentation System:** See [3.1 - Three-File System Overview](../../03-Three-File-System/3.1-system-overview.md)
2. **Understand Content Structure:** See [2.3 - Essential Dimensions Framework](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)
3. **Start Implementation:** See [6.1 - Component Selection Guide](../../06-Implementation-Guide/6.1-component-selection-guide.md)

---

*This entity model transforms software architecture from implicit tribal knowledge into an explicit, navigable system that serves both human understanding and machine automation.*
