# 4. Version Your Contracts

#### What This Means: Managing Changes Like Legal Agreements

Imagine you're renting an apartment and your landlord wants to change the lease agreement. They can't just show up one day and say "By the way, rent is now double and pets are banned." That would break your existing contract and cause chaos. Instead, they need to:

1. **Honor the current lease** until it expires
2. **Propose a new lease** with the changes clearly marked
3. **Give you time to decide** whether to accept the new terms or move out
4. **Provide a transition period** if you choose to leave

API versioning works exactly the same way. When you need to change how your software components communicate, you can't just change the rules overnight and expect everything to keep working. You need to manage the transition carefully, just like updating a legal contract.

**Contract versioning** means treating your APIs like formal agreements between software components. When you need to make changes, you create a new version of the "contract" while keeping the old version available until everyone has had time to upgrade.

#### Key Terms Made Simple

**API Contract:** The formal agreement about how two pieces of software will communicate. Like a lease agreement, it specifies exactly what each party can expect from the other.

**Breaking Change:** A modification that would cause existing users of your API to stop working. Like changing the locks on an apartment without giving tenants new keys.

**Non-Breaking Change:** A modification that doesn't affect existing users. Like adding a new amenity to an apartment building—existing tenants benefit but nothing they currently do stops working.

**Deprecation:** The process of phasing out an old version of an API. Like giving tenants notice that a lease won't be renewed, providing time to make other arrangements.

**Backward Compatibility:** Ensuring that new versions of your API still work with older clients. Like honoring existing lease terms even when you offer new lease options.

#### The Lease Agreement Analogy

Let's use apartment lease agreements to understand different types of API changes:

**Non-Breaking Changes (Lease Improvements):**
```
✅ Adding: "Free Wi-Fi now included"
✅ Adding: "New gym facility available"  
✅ Adding: "Optional parking spaces available"
✅ Result: Existing tenants benefit, nothing breaks
```

**Breaking Changes (Lease Violations):**
```
❌ Changing: "Rent payment now required weekly instead of monthly"
❌ Removing: "Parking is no longer included"
❌ Changing: "All communication must now be via email only (no phone)"
❌ Result: Existing tenants can't comply with current lease terms
```

**Proper Change Management (New Lease Version):**
```
✅ Current lease: "Lease v1.0 - Monthly rent, parking included"
✅ New lease offered: "Lease v2.0 - Weekly rent, parking extra"
✅ Transition plan: "v1.0 honored until expiration, v2.0 optional"
✅ Result: Tenants can choose when and if to upgrade
```

#### Understanding Breaking Changes with Concrete Examples

Breaking changes are modifications that would cause existing API consumers to fail. Here are real-world examples:

##### Example 1: Changing Required Fields

**❌ Breaking Change:**
```yaml
# API v1.0 - Original contract
POST /api/users
{
  "name": "John Doe",
  "email": "<EMAIL>"
}

# API v2.0 - BREAKING: Now requires phone number
POST /api/users  
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "555-1234"  # This field is now required!
}
```

**Why it breaks:** Existing clients don't know they need to send a phone number, so their requests will fail.

**✅ Non-Breaking Alternative:**
```yaml
# API v1.0 - Still works exactly as before
POST /api/v1/users
{
  "name": "John Doe", 
  "email": "<EMAIL>"
}

# API v2.0 - New version with additional field
POST /api/v2/users
{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "phone": "555-1234"  # Required in v2, but v1 still available
}
```

##### Example 2: Changing Response Format

**❌ Breaking Change:**
```yaml
# API v1.0 - Original response format
GET /api/user/123
Response: {
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>"
}

# API v2.0 - BREAKING: Changed response structure  
GET /api/user/123
Response: {
  "user": {                    # Wrapped in "user" object
    "id": 123,
    "fullName": "John Doe",    # "name" renamed to "fullName"
    "email": "<EMAIL>"
  }
}
```

**Why it breaks:** Existing clients expect `response.name` but now need `response.user.fullName`.

##### Example 3: Removing Functionality

**❌ Breaking Change:**
```yaml
# API v1.0 - Original endpoints
GET /api/users          # List all users
GET /api/users/123      # Get specific user
DELETE /api/users/123   # Delete user

# API v2.0 - BREAKING: Removed delete functionality
GET /api/users          # Still works
GET /api/users/123      # Still works  
DELETE /api/users/123   # REMOVED - returns 404 error
```

**Why it breaks:** Existing clients that try to delete users will receive error responses.

#### Versioning Strategy Overview

Just like lease agreements need clear version numbers and terms, APIs need systematic versioning strategies. Here are the most common approaches:

##### URL Path Versioning (Most Common)
```yaml
# Clear version in the URL path
GET /api/v1/users       # Version 1
GET /api/v2/users       # Version 2
GET /api/v3/users       # Version 3

# Benefits:
# - Version is immediately visible
# - Easy to route different versions to different code
# - Simple for developers to understand and use
```

##### Header Versioning (More Flexible)
```yaml
# Version specified in HTTP header
GET /api/users
Headers: 
  API-Version: v1

GET /api/users  
Headers:
  API-Version: v2

# Benefits:
# - URLs stay clean
# - Can version individual endpoints differently
# - More sophisticated routing options
```

##### Query Parameter Versioning (Simple)
```yaml
# Version as URL parameter
GET /api/users?version=v1
GET /api/users?version=v2

# Benefits:
# - Easy to implement
# - Version is visible in URLs
# - Simple for testing different versions
```

#### Naming Conventions and Best Practices

**Version Numbering:**
- **Semantic Versioning:** v1.0.0, v1.1.0, v2.0.0
  - Major version (v1 → v2): Breaking changes
  - Minor version (v1.0 → v1.1): New features, no breaking changes
  - Patch version (v1.0.0 → v1.0.1): Bug fixes only

- **Simple Versioning:** v1, v2, v3
  - Easier to understand for beginners
  - Each version represents a significant milestone
  - Good for APIs with infrequent major changes

**Documentation Standards:**
```yaml
# Always document what changed between versions
API v1.0:
  - Initial release
  - Basic user management
  - Email notifications

API v2.0:
  - BREAKING: Phone number now required for user creation
  - BREAKING: Response format changed for GET /users
  - Added: SMS notifications
  - Added: User profile pictures
  - Deprecated: Email-only notification preferences
```

**Migration Guides:**
```yaml
# Provide clear upgrade instructions
Migrating from v1 to v2:

1. Update user creation calls:
   OLD: POST /api/v1/users {"name": "John", "email": "<EMAIL>"}
   NEW: POST /api/v2/users {"name": "John", "email": "<EMAIL>", "phone": "555-1234"}

2. Update response parsing:
   OLD: response.name
   NEW: response.user.fullName

3. Test thoroughly in staging environment
4. Deploy during low-traffic period
5. Monitor error rates after deployment
```

This foundation gives you the conceptual understanding and practical knowledge needed to manage API changes safely, just like a good property manager handles lease updates professionally and considerately.

#### How to Do It: Step-by-Step Versioning Workflow

Managing API versions requires a systematic approach, just like managing lease renewals. Here's a practical workflow that ensures smooth transitions:

##### Step 1: Plan Your Version Strategy

Before making any changes, establish your versioning approach:

```yaml
# Document your versioning strategy in your API's catalog-info.yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-management-api
  annotations:
    # Document your versioning approach
    cortex.io/versioning-strategy: "url-path"
    cortex.io/current-version: "v1"
    cortex.io/supported-versions: "v1"
spec:
  type: api
  lifecycle: production
  owner: user-team
  definition: |
    # API Versioning Strategy
    - URL Path Versioning: /api/v1/, /api/v2/
    - Breaking changes require new major version
    - Non-breaking changes increment minor version
    - Support 2 major versions simultaneously
    - 6-month deprecation notice for old versions
```

##### Step 2: Implement Version-Aware Endpoints

Create your API endpoints with explicit version paths:

```yaml
# Example: User Management API Implementation

# Version 1 - Original API
GET /api/v1/users
POST /api/v1/users
PUT /api/v1/users/{id}
DELETE /api/v1/users/{id}

# Version 2 - Enhanced API (when needed)
GET /api/v2/users
POST /api/v2/users  
PUT /api/v2/users/{id}
DELETE /api/v2/users/{id}

# Both versions can coexist and serve different clients
```

##### Step 3: Document API Contracts in catalog-info.yaml

Make your API versions explicit in your documentation:

```yaml
# user-management-api catalog-info.yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-management-api
  description: Provides user account management functionality
spec:
  type: api
  lifecycle: production
  owner: user-team
  
  # Document which services provide this API
  providedBy:
    - component:default/user-service
  
  # Document API versions and their status
  definition: |
    openapi: 3.0.0
    info:
      title: User Management API
      version: 2.0.0
      description: |
        ## Supported Versions
        
        ### v2 (Current) - Recommended for new integrations
        - Path: /api/v2/
        - Status: Active development
        - Features: Enhanced user profiles, phone number support
        
        ### v1 (Legacy) - Deprecated December 2024
        - Path: /api/v1/
        - Status: Maintenance only
        - Deprecation: Will be removed June 2025
        - Migration guide: See /docs/v1-to-v2-migration
    
    paths:
      # v2 endpoints
      /api/v2/users:
        post:
          summary: Create new user (v2)
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required: [name, email, phone]
                  properties:
                    name: {type: string}
                    email: {type: string, format: email}
                    phone: {type: string}  # Required in v2
      
      # v1 endpoints (for backward compatibility)
      /api/v1/users:
        post:
          summary: Create new user (v1 - DEPRECATED)
          deprecated: true
          description: "Use /api/v2/users for new integrations"
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required: [name, email]
                  properties:
                    name: {type: string}
                    email: {type: string, format: email}
```

##### Step 4: Create Migration Documentation Templates

Provide clear guidance for consumers upgrading between versions:

```yaml
# Create a migration guide document
# File: docs/api-migration-v1-to-v2.md

# Migration Guide: User Management API v1 → v2

## Overview
This guide helps you upgrade from User Management API v1 to v2.

## Timeline
- **v2 Available:** January 2024
- **v1 Deprecation Notice:** December 2024  
- **v1 End of Life:** June 2025

## Breaking Changes

### 1. Phone Number Now Required
**v1 Request:**
```json
POST /api/v1/users
{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

**v2 Request:**
```json
POST /api/v2/users
{
  "name": "John Doe", 
  "email": "<EMAIL>",
  "phone": "******-123-4567"  // Now required
}
```

### 2. Response Format Enhanced
**v1 Response:**
```json
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "created": "2024-01-15T10:30:00Z"
}
```

**v2 Response:**
```json
{
  "user": {
    "id": 123,
    "profile": {
      "fullName": "John Doe",      // "name" → "fullName"
      "email": "<EMAIL>",
      "phone": "******-123-4567"
    },
    "metadata": {
      "created": "2024-01-15T10:30:00Z",
      "lastUpdated": "2024-01-15T10:30:00Z"  // New field
    }
  }
}
```

## Step-by-Step Migration

### Phase 1: Preparation (Week 1)
1. **Review Changes:** Read this migration guide completely
2. **Update Dependencies:** Ensure your HTTP client supports custom headers
3. **Plan Testing:** Identify all places your code calls the User API
4. **Set Up Staging:** Configure staging environment to test v2

### Phase 2: Code Updates (Week 2-3)
1. **Update Request Format:**
   ```javascript
   // OLD v1 code
   const response = await fetch('/api/v1/users', {
     method: 'POST',
     body: JSON.stringify({
       name: userData.name,
       email: userData.email
     })
   });
   
   // NEW v2 code
   const response = await fetch('/api/v2/users', {
     method: 'POST', 
     body: JSON.stringify({
       name: userData.name,
       email: userData.email,
       phone: userData.phone  // Add phone number
     })
   });
   ```

2. **Update Response Parsing:**
   ```javascript
   // OLD v1 parsing
   const user = await response.json();
   console.log(user.name);  // "John Doe"
   
   // NEW v2 parsing  
   const result = await response.json();
   console.log(result.user.profile.fullName);  // "John Doe"
   ```

### Phase 3: Testing (Week 4)
1. **Unit Tests:** Update all API-related unit tests
2. **Integration Tests:** Test full user creation flow
3. **Staging Validation:** Run complete test suite against v2 API
4. **Performance Testing:** Verify v2 performance meets requirements

### Phase 4: Deployment (Week 5)
1. **Deploy During Low Traffic:** Schedule deployment for off-peak hours
2. **Monitor Closely:** Watch error rates and response times
3. **Have Rollback Plan:** Be ready to revert to v1 if issues arise
4. **Update Documentation:** Update your internal API documentation

## Common Migration Issues

### Issue 1: Missing Phone Numbers
**Problem:** Existing users don't have phone numbers in your database
**Solution:** 
```javascript
// Handle missing phone data gracefully
const phoneNumber = userData.phone || "************";  // Default value
// Or prompt users to add phone numbers before migration
```

### Issue 2: Response Parsing Errors
**Problem:** Code expects flat response but gets nested object
**Solution:**
```javascript
// Create adapter function for gradual migration
function parseUserResponse(response, apiVersion) {
  if (apiVersion === 'v1') {
    return {
      id: response.id,
      name: response.name,
      email: response.email
    };
  } else {
    return {
      id: response.user.id,
      name: response.user.profile.fullName,
      email: response.user.profile.email
    };
  }
}
```

## Support and Help
- **Questions:** Contact <EMAIL>
- **Issues:** Create ticket in JIRA project USER-API
- **Office Hours:** Tuesdays 2-3pm for migration support
```

##### Step 5: Implement Deprecation Communication Strategy

Create a systematic approach to communicating API changes:

**Deprecation Timeline Template:**
```yaml
# API Deprecation Communication Plan

## 6 Months Before Removal
- [ ] Add deprecation warnings to API responses
- [ ] Send email to all registered API consumers
- [ ] Update API documentation with deprecation notice
- [ ] Post announcement in developer Slack channels

## 3 Months Before Removal  
- [ ] Increase deprecation warning frequency
- [ ] Send follow-up emails to consumers still using old version
- [ ] Offer migration support office hours
- [ ] Update error messages to include migration timeline

## 1 Month Before Removal
- [ ] Send final warning emails
- [ ] Add prominent deprecation banners to documentation
- [ ] Prepare rollback plan in case of issues
- [ ] Schedule removal during low-traffic period

## Day of Removal
- [ ] Remove old API version
- [ ] Monitor error rates closely
- [ ] Be ready to restore old version if critical issues arise
- [ ] Send confirmation email that removal is complete
```

**Automated Deprecation Headers:**
```yaml
# Add deprecation information to HTTP responses
HTTP/1.1 200 OK
Deprecation: true
Sunset: "2025-06-01T00:00:00Z"
Link: </docs/v1-to-v2-migration>; rel="successor-version"
Warning: 299 - "API v1 is deprecated. Migrate to v2 by June 2025. See migration guide."

{
  "data": { ... },
  "_deprecation": {
    "version": "v1",
    "deprecated": true,
    "sunset": "2025-06-01T00:00:00Z",
    "migrationGuide": "https://docs.company.com/api/v1-to-v2-migration",
    "supportContact": "<EMAIL>"
  }
}
```

This systematic approach ensures that API changes are managed professionally, giving consumers adequate time and support to migrate successfully.

#### Versioning Planning and Management

Just like a property management company needs clear policies for handling lease changes, your team needs systematic approaches for planning and managing API versions. This section provides decision-making frameworks and best practices for long-term API evolution.

##### Versioning Decision Matrix

Use this matrix to determine what type of version change you need based on the type of modification you're making:

| Change Type | Examples | Version Impact | Action Required |
|-------------|----------|----------------|-----------------|
| **Bug Fix** | Fix calculation error, correct typo in response | Patch (v1.0.1) | Deploy immediately, no consumer changes needed |
| **New Optional Field** | Add optional "avatar_url" to user response | Minor (v1.1.0) | Deploy immediately, consumers benefit automatically |
| **New Endpoint** | Add GET /api/v1/users/{id}/preferences | Minor (v1.1.0) | Deploy immediately, document new capability |
| **New Required Field** | Require "phone" in user creation request | Major (v2.0.0) | Create new version, maintain old version, plan migration |
| **Remove Field** | Remove "legacy_id" from user response | Major (v2.0.0) | Create new version, maintain old version, plan migration |
| **Change Field Name** | Rename "name" to "fullName" in response | Major (v2.0.0) | Create new version, maintain old version, plan migration |
| **Change Field Type** | Change "age" from string to number | Major (v2.0.0) | Create new version, maintain old version, plan migration |
| **Remove Endpoint** | Remove deprecated GET /api/v1/legacy-users | Major (v2.0.0) | Create new version, maintain old version, plan migration |

##### Quick Decision Guide

**Ask yourself these questions:**

1. **Will existing clients break if I deploy this change?**
   - **No** → Patch or Minor version
   - **Yes** → Major version (new API version required)

2. **Am I adding something new without changing existing behavior?**
   - **Yes** → Minor version
   - **No** → Continue to question 3

3. **Am I fixing a bug without changing the API contract?**
   - **Yes** → Patch version
   - **No** → Major version

4. **Would I be comfortable if someone deployed this change to an API I depend on without warning me?**
   - **Yes** → Safe to deploy as minor/patch
   - **No** → Major version with migration plan

##### Backward Compatibility Assessment Guide

Before making any API change, use this checklist to assess backward compatibility:

**✅ Backward Compatible Changes (Safe to deploy):**
- [ ] Adding new optional fields to responses
- [ ] Adding new optional parameters to requests
- [ ] Adding new endpoints
- [ ] Adding new HTTP methods to existing endpoints
- [ ] Making required fields optional
- [ ] Fixing bugs that don't change expected behavior
- [ ] Adding new enum values (if clients handle unknown values gracefully)
- [ ] Improving performance without changing functionality

**❌ Breaking Changes (Require new version):**
- [ ] Removing fields from responses
- [ ] Removing endpoints
- [ ] Removing HTTP methods
- [ ] Making optional fields required
- [ ] Changing field names
- [ ] Changing field types
- [ ] Changing response structure (nesting, flattening)
- [ ] Changing error response formats
- [ ] Changing authentication requirements
- [ ] Changing rate limiting rules

**⚠️ Potentially Breaking Changes (Assess carefully):**
- [ ] Adding validation rules to existing fields
- [ ] Changing default values
- [ ] Adding new required headers
- [ ] Changing URL patterns
- [ ] Modifying business logic that affects response values

##### Version Lifecycle Management Best Practices

**Planning Phase:**
```yaml
# Document version lifecycle in your API specification
API Version Lifecycle Plan:

v1.0 (Current Production):
  - Status: Active development
  - Support Level: Full support
  - End of Life: TBD (when v2 is stable)

v2.0 (In Development):
  - Status: Beta testing
  - Support Level: Development only
  - Target Release: Q2 2024
  - Migration Period: 6 months
  
v0.9 (Legacy):
  - Status: Deprecated
  - Support Level: Security fixes only
  - End of Life: March 2024
  - Migration Deadline: February 2024
```

**Support Matrix:**
```yaml
# Define what level of support each version receives
Version Support Levels:

Full Support:
  - New features added
  - Bug fixes provided
  - Performance improvements
  - Security updates
  - Documentation maintained
  - Developer support available

Maintenance Support:
  - Critical bug fixes only
  - Security updates
  - No new features
  - Limited documentation updates
  - Basic developer support

Deprecated:
  - Security fixes only
  - No bug fixes for non-security issues
  - No new features
  - Documentation marked as deprecated
  - No developer support
  - Removal date announced
```

**Migration Timeline Template:**
```yaml
# Standard 6-month migration timeline
API Version Migration Timeline:

Month 1-2: Preparation
  - [ ] Release new version in beta
  - [ ] Create migration documentation
  - [ ] Set up monitoring for both versions
  - [ ] Announce new version to consumers

Month 3-4: Active Migration
  - [ ] Promote new version to production
  - [ ] Begin deprecation warnings on old version
  - [ ] Offer migration support office hours
  - [ ] Track adoption metrics

Month 5: Final Push
  - [ ] Increase deprecation warning frequency
  - [ ] Contact consumers still on old version
  - [ ] Prepare for old version removal
  - [ ] Final testing of removal process

Month 6: Removal
  - [ ] Remove old version
  - [ ] Monitor for issues
  - [ ] Update documentation
  - [ ] Celebrate successful migration!
```

##### Common Versioning Mistakes and How to Avoid Them

**Mistake 1: Making Breaking Changes in Minor Versions**
```yaml
❌ Wrong:
# v1.1.0 - BREAKING: Changed response format
GET /api/v1/users/123
Response: {
  "user": {  # Added wrapper - breaks existing clients!
    "id": 123,
    "name": "John"
  }
}

✅ Right:
# v1.1.0 - Added optional field only
GET /api/v1/users/123  
Response: {
  "id": 123,
  "name": "John",
  "avatar": "https://..."  # New optional field - safe
}
```

**Mistake 2: Not Planning for Multiple Version Support**
```yaml
❌ Wrong Approach:
- Deploy v2, immediately remove v1
- Force all consumers to upgrade at once
- No transition period

✅ Right Approach:
- Deploy v2 alongside v1
- Support both versions for 6 months
- Gradual migration with support
- Remove v1 only after all consumers migrate
```

**Mistake 3: Poor Communication About Changes**
```yaml
❌ Wrong Communication:
- Surprise breaking changes
- No migration documentation
- Short or no deprecation notice
- No support during migration

✅ Right Communication:
- 6-month advance notice
- Detailed migration guides
- Regular status updates
- Dedicated support during transition
```

**Mistake 4: Versioning Everything**
```yaml
❌ Over-versioning:
v1.0.0 → v1.0.1 → v1.0.2 → v1.1.0 → v1.1.1 → v2.0.0 → v2.0.1...
# Too many versions confuse consumers

✅ Strategic Versioning:
v1 → v2 → v3
# Major versions only for significant changes
# Patch/minor changes deployed without version changes
```

##### Version Lifecycle Monitoring

Track these metrics to manage API versions effectively:

**Adoption Metrics:**
```yaml
# Track version usage over time
API Version Usage Dashboard:

v1 Usage:
  - Total Requests: 45,000/day (↓ 15% from last month)
  - Active Consumers: 12 services (↓ 3 from last month)
  - Error Rate: 0.2%

v2 Usage:
  - Total Requests: 85,000/day (↑ 35% from last month)
  - Active Consumers: 23 services (↑ 8 from last month)  
  - Error Rate: 0.1%

Migration Progress:
  - Target: 100% migration to v2 by June 2024
  - Current: 65% of traffic on v2
  - Remaining: 12 services need to migrate
```

**Health Metrics:**
```yaml
# Monitor version health and stability
Version Health Dashboard:

Performance:
  - v1 Average Response Time: 150ms
  - v2 Average Response Time: 120ms
  - v1 95th Percentile: 300ms
  - v2 95th Percentile: 250ms

Reliability:
  - v1 Uptime: 99.9%
  - v2 Uptime: 99.95%
  - v1 Error Rate: 0.2%
  - v2 Error Rate: 0.1%

Support Load:
  - v1 Support Tickets: 5/week
  - v2 Support Tickets: 2/week
  - Migration Questions: 8/week
```

This comprehensive approach to versioning planning and management ensures that your API evolution is predictable, supportive, and professional—just like the best property management companies handle lease transitions.

#### Common Mistakes and How to Fix Them

##### Mistake 1: Surprise Breaking Changes

**❌ What People Do Wrong:**
```yaml
# Deploy breaking changes without warning
# v1.2.0 - BREAKING: Phone number now required
POST /api/v1/users
{
  "name": "John",
  "email": "<EMAIL>",
  "phone": "555-1234"  # Suddenly required!
}
```

**✅ The Right Way:**
```yaml
# Create new version for breaking changes
# v1.2.0 - Added optional phone field (backward compatible)
POST /api/v1/users
{
  "name": "John", 
  "email": "<EMAIL>",
  "phone": "555-1234"  # Optional in v1
}

# v2.0.0 - Phone required in new version
POST /api/v2/users
{
  "name": "John",
  "email": "<EMAIL>", 
  "phone": "555-1234"  # Required in v2
}
```

**Why This Matters:** Breaking changes without version bumps break existing clients immediately.

##### Mistake 2: No Migration Support

**❌ What People Do Wrong:**
- Release v2 and immediately remove v1
- Provide no migration documentation
- Offer no support during transition

**✅ The Right Way:**
- Support both versions for 6+ months
- Provide detailed migration guides
- Offer office hours for migration questions
- Monitor adoption and provide assistance

##### Mistake 3: Inconsistent Versioning Strategy

**❌ What People Do Wrong:**
```yaml
# Mixing versioning approaches randomly
GET /api/v1/users        # URL versioning
GET /api/orders          # No versioning
Headers: API-Version: v2 # Header versioning for some endpoints
```

**✅ The Right Way:**
```yaml
# Consistent URL versioning across all endpoints
GET /api/v1/users
GET /api/v1/orders
GET /api/v2/users
GET /api/v2/orders
```

#### Troubleshooting Common Versioning Problems

##### Problem: "Consumers aren't migrating to the new version"

**Symptoms:**
- New API version released months ago but low adoption
- Old version still receiving majority of traffic
- Consumers asking for extended support of old version

**Diagnosis Steps:**
1. Survey consumers about migration blockers
2. Review migration documentation for clarity
3. Check if new version has performance issues
4. Assess if breaking changes are too significant

**Solutions:**
```yaml
# Make migration easier
- Provide automated migration tools
- Offer backward compatibility layers
- Create step-by-step migration workshops
- Assign dedicated migration support person

# Example: Compatibility layer
GET /api/v1/users  # Old format
→ Internally calls v2 API and transforms response
→ Allows gradual migration without breaking existing clients
```

##### Problem: "Too many API versions to maintain"

**Symptoms:**
- Supporting 4+ versions simultaneously
- High maintenance overhead
- Confusion about which version to use
- Inconsistent behavior across versions

**Solutions:**
```yaml
# Version consolidation strategy
1. Audit all versions and their usage
2. Identify versions with <5% traffic
3. Create aggressive migration timeline for low-usage versions
4. Implement "version sunset" policy (max 2 active versions)

# Example sunset policy
Version Lifecycle:
- New version released
- Previous version enters "maintenance mode" (6 months)
- Previous version enters "deprecated mode" (3 months)
- Previous version removed
- Maximum 2 versions supported at any time
```

#### Quick Check Exercise

Test your understanding with this versioning scenario:

**Scenario:** You maintain a "product-catalog-api" that currently returns:
```json
GET /api/v1/products/123
{
  "id": 123,
  "name": "Laptop",
  "price": 999.99,
  "category": "electronics"
}
```

You need to make these changes:
1. Add optional "description" field
2. Change "price" from number to object with currency info
3. Add new endpoint for product reviews

**Challenge:** Plan the versioning strategy for these changes.

**Your Plan:**
```yaml
# What version changes do you need?
# Which changes are breaking vs non-breaking?
# How would you structure the new API?
```

**Solution** (Don't peek until you've tried!):
```yaml
# Version 1.1 (Non-breaking changes)
GET /api/v1/products/123
{
  "id": 123,
  "name": "Laptop", 
  "price": 999.99,
  "category": "electronics",
  "description": "High-performance laptop"  # New optional field
}

GET /api/v1/products/123/reviews  # New endpoint - non-breaking

# Version 2.0 (Breaking changes)
GET /api/v2/products/123
{
  "id": 123,
  "name": "Laptop",
  "price": {                    # BREAKING: Changed from number to object
    "amount": 999.99,
    "currency": "USD"
  },
  "category": "electronics",
  "description": "High-performance laptop"
}

# Migration plan:
# 1. Deploy v1.1 with optional description and reviews endpoint
# 2. Deploy v2.0 with new price format
# 3. Support both v1 and v2 for 6 months
# 4. Deprecate v1 after migration period
```

This systematic approach to API versioning ensures that your software contracts evolve professionally, maintaining trust with consumers while enabling necessary improvements—just like the best legal contracts balance stability with the need for change.
