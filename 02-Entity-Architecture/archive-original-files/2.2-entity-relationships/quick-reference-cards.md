# Quick Reference Cards: Relationship Best Practices

## 🎯 Relationship Types Quick Reference Card

### Core Relationship Types
| Relationship | When to Use | Example |
|-------------|-------------|---------|
| `dependsOn` | "I need this to function" | Service → Database |
| `consumesApis` | "I call this API" | Web App → User API |
| `providesApis` | "I offer this API" | Auth Service → Auth API |
| `implementedBy` | "These components provide this feature" | User Management → User Service |
| `contains` | "This system includes these features" | E-commerce → Shopping Cart |

### Quick Decision Tree
```
Does your component...
├─ Need another component to work? → Use `dependsOn`
├─ Call another service's API? → Use `consumesApis`
├─ Expose an API for others? → Use `providesApis`
├─ Implement a business feature? → Feature uses `implementedBy`
└─ Group related features? → System uses `contains`
```

---

## 🔗 Explicit Relationships Checklist

### ✅ Before You Deploy
- [ ] All database dependencies documented in `dependsOn`
- [ ] All library dependencies documented in `dependsOn`
- [ ] All API calls documented in `consumesApis`
- [ ] All exposed APIs documented in `providesApis`
- [ ] All relationships have explanatory comments
- [ ] No "mystery" dependencies that aren't documented

### ✅ Documentation Quality Check
- [ ] Relationship names are specific (not generic like "database")
- [ ] Comments explain WHY, not just WHAT
- [ ] All technical terms are defined for newcomers
- [ ] Examples show both correct and incorrect approaches
- [ ] Troubleshooting guidance is provided

### ✅ Team Process Check
- [ ] New services require relationship documentation before deployment
- [ ] Code reviews include relationship documentation review
- [ ] Relationship changes are communicated to affected teams
- [ ] Documentation is updated when code dependencies change

---

## 🔄 Coupling Assessment Quick Card

### Coupling Warning Signs
| 🚨 Red Flags | 🟡 Yellow Flags | ✅ Green Flags |
|-------------|----------------|---------------|
| Services share databases | Services call each other directly | Services use APIs only |
| Circular dependencies | Large shared libraries | Focused, single-purpose libraries |
| Can't deploy independently | Tight release coordination needed | Independent deployment possible |
| Changes break other services | Changes require extensive testing | Changes have predictable impact |

### Decoupling Decision Matrix
```
Problem Type → Solution
├─ Shared Database → Separate databases + APIs
├─ Direct Method Calls → REST APIs or Events
├─ Circular Dependencies → Extract shared logic to library
├─ Monolithic Library → Split into focused libraries
└─ Synchronous Coupling → Asynchronous events
```

### Quick Coupling Test
Ask these questions about any two components:
1. Can I deploy them independently? (Should be YES)
2. Can I change one without affecting the other? (Should be YES)
3. Do they share data storage? (Should be NO)
4. Do they make direct method calls? (Should be NO)

---

## 🏗️ Hierarchy Rules Quick Reference

### The Golden Rules
```
System (Business Domain)
  └── contains → Feature (User Capability)
        └── implementedBy → Component (Technical Implementation)
              └── dependsOn → Resource (Infrastructure)
```

### Hierarchy Validation Checklist
- [ ] Systems contain Features (not Components directly)
- [ ] Features are implemented by Components (not Systems)
- [ ] Components depend on Resources and other Components
- [ ] No circular containment (A contains B, B contains A)
- [ ] Each entity has a clear owner and purpose

### Common Hierarchy Mistakes
| ❌ Wrong | ✅ Right | Why |
|---------|----------|-----|
| System → Component | System → Feature → Component | Systems group business capabilities |
| Feature → System | Feature → Component | Features are implemented by technical components |
| Component → Feature | Component → Component/Resource | Components are technical building blocks |

### Hierarchy Decision Flowchart
```
What am I organizing?
├─ Business capability serving users? → Feature
├─ Collection of related features? → System  
├─ Technical implementation? → Component
└─ Infrastructure dependency? → Resource
```

---

## 📋 Common Pitfalls Prevention Checklist

### Explicit Relationships Pitfalls
- [ ] **Avoid**: Documenting only some relationships
  - **Fix**: Document ALL dependencies, APIs, and resources
- [ ] **Avoid**: Using vague names like "database" or "service"
  - **Fix**: Use specific names like "postgres-user-profiles-db"
- [ ] **Avoid**: No comments explaining business purpose
  - **Fix**: Add "Why" comments for every relationship
- [ ] **Avoid**: Forgetting to update docs when code changes
  - **Fix**: Include relationship updates in code review process

### Coupling Pitfalls
- [ ] **Avoid**: Services sharing databases
  - **Fix**: Each service owns its data, uses APIs to access others
- [ ] **Avoid**: Creating "god" libraries with everything
  - **Fix**: Split into focused, single-purpose libraries
- [ ] **Avoid**: Direct service-to-service method calls
  - **Fix**: Use REST APIs or event-driven communication
- [ ] **Avoid**: Circular dependencies between services
  - **Fix**: Extract shared logic or redesign service boundaries

### Hierarchy Pitfalls
- [ ] **Avoid**: Systems containing Components directly
  - **Fix**: Systems contain Features, Features are implemented by Components
- [ ] **Avoid**: Features implemented by Systems
  - **Fix**: Features are implemented by Components
- [ ] **Avoid**: Organizing by technology instead of business value
  - **Fix**: Group by user-facing capabilities and business domains

### Versioning Pitfalls
- [ ] **Avoid**: Making breaking changes without versioning
  - **Fix**: Always version breaking changes, maintain backward compatibility
- [ ] **Avoid**: No migration plan for API consumers
  - **Fix**: Provide clear migration guides and timelines
- [ ] **Avoid**: Removing old versions too quickly
  - **Fix**: Give consumers adequate time to migrate (3-6 months minimum)

---

## 🚀 API Versioning Quick Decision Guide

### Is This Change Breaking?
| Change Type | Breaking? | Version Impact |
|------------|-----------|----------------|
| Add optional field | ❌ No | Minor (v1.1) |
| Add required field | ✅ Yes | Major (v2.0) |
| Remove any field | ✅ Yes | Major (v2.0) |
| Change field type | ✅ Yes | Major (v2.0) |
| Change field name | ✅ Yes | Major (v2.0) |
| Fix bug, same interface | ❌ No | Patch (v1.0.1) |
| Add new endpoint | ❌ No | Minor (v1.1) |
| Remove endpoint | ✅ Yes | Major (v2.0) |

### Versioning Strategy Selector
```
Consumer Type → Strategy
├─ Internal APIs → Coordinate releases, shorter deprecation
├─ Partner APIs → Longer deprecation, clear communication
├─ Public APIs → Very long deprecation, extensive documentation
└─ Mobile APIs → Extra long deprecation, gradual rollout
```

### Migration Timeline Template
```
Month 1-2: New version available
├─ Deploy new version alongside old
├─ Update documentation
└─ Notify consumers

Month 3-4: Active migration
├─ Provide migration support
├─ Monitor adoption metrics
└─ Address consumer issues

Month 5-6: Deprecation warnings
├─ Add deprecation headers
├─ Send final notifications
└─ Set end-of-life date

Month 7+: Old version removal
├─ Remove old endpoints
├─ Monitor for stragglers
└─ Provide emergency support
```

---

## 🎯 Decision-Making Flowcharts

### Relationship Documentation Decision Tree
```
New Component Created
├─ Does it need a database? → Add to `dependsOn`
├─ Does it call other services? → Add to `consumesApis`
├─ Does it expose APIs? → Add to `providesApis`
├─ Does it use libraries? → Add to `dependsOn`
├─ Does it implement a feature? → Feature uses `implementedBy`
└─ Does it belong to a system? → System uses `contains`
```

### Coupling Problem Resolution Flowchart
```
Coupling Problem Identified
├─ Services share database?
│   └─ Split databases, add APIs
├─ Circular dependencies?
│   └─ Extract shared logic or redesign boundaries
├─ Direct method calls?
│   └─ Replace with REST APIs or events
├─ Monolithic library?
│   └─ Split into focused libraries
└─ Can't deploy independently?
    └─ Identify and break blocking dependencies
```

### Hierarchy Organization Flowchart
```
New Entity to Organize
├─ Serves end users directly? → Feature
├─ Groups related features? → System
├─ Technical implementation? → Component
├─ Infrastructure dependency? → Resource
└─ Unsure? → Start with Component, refactor later
```

### API Change Impact Flowchart
```
API Change Needed
├─ Breaks existing consumers?
│   ├─ Yes → Major version, migration plan needed
│   └─ No → Minor/patch version, deploy safely
├─ Adds new functionality?
│   ├─ Required by consumers → Major version
│   └─ Optional for consumers → Minor version
└─ Fixes bug without interface change?
    └─ Patch version, deploy immediately
```

---

## 📊 Assessment Scorecards

### Relationship Documentation Maturity Score
Rate each area 1-5 (1=Poor, 5=Excellent):

**Completeness** ___/5
- All dependencies documented
- All APIs documented  
- All resources documented

**Clarity** ___/5
- Specific, descriptive names
- Clear explanatory comments
- Easy to understand for newcomers

**Accuracy** ___/5
- Documentation matches actual code
- Updated when code changes
- No outdated or incorrect relationships

**Usefulness** ___/5
- Helps with debugging
- Speeds up onboarding
- Enables confident changes

**Total Score: ___/20**
- 16-20: Excellent relationship documentation
- 12-15: Good, minor improvements needed
- 8-11: Adequate, significant improvements needed
- 4-7: Poor, major overhaul required
- 0-3: Critical, start documenting immediately

### Coupling Health Score
Rate each area 1-5 (1=Tightly Coupled, 5=Loosely Coupled):

**Database Independence** ___/5
- Services own their data
- No shared databases
- APIs used for data access

**Deployment Independence** ___/5
- Services deploy separately
- No coordinated releases required
- Changes don't break other services

**Library Design** ___/5
- Focused, single-purpose libraries
- No monolithic shared libraries
- Services include only what they need

**Communication Patterns** ___/5
- APIs or events for communication
- No direct method calls
- Clear interface contracts

**Total Score: ___/20**
- 16-20: Excellent loose coupling
- 12-15: Good coupling practices
- 8-11: Some coupling issues to address
- 4-7: Significant coupling problems
- 0-3: Tightly coupled, needs major refactoring

---

## 🛠️ Emergency Troubleshooting Cards

### "My Service Keeps Failing" Debug Card
1. **Check Dependencies**: Are all `dependsOn` components running?
2. **Verify APIs**: Are all `consumesApis` endpoints responding?
3. **Test Resources**: Can you connect to all databases/queues?
4. **Review Recent Changes**: Did any dependencies change recently?
5. **Check Versions**: Are you using compatible API versions?

### "I Don't Know What Will Break" Impact Card
1. **Find Consumers**: Who uses your `providesApis`?
2. **Check Dependents**: What has you in their `dependsOn`?
3. **Review Relationships**: What other services call your APIs?
4. **Test Scenarios**: Create test cases for each relationship
5. **Plan Rollback**: Have a quick rollback strategy ready

### "New Person Can't Understand System" Onboarding Card
1. **Start with Systems**: Show the big picture business domains
2. **Drill into Features**: Explain user-facing capabilities
3. **Show Components**: Walk through technical implementation
4. **Follow Relationships**: Trace data flow and dependencies
5. **Provide Examples**: Use real scenarios they'll work on

### "Teams Keep Breaking Each Other" Coordination Card
1. **Document All APIs**: Make all interfaces explicit
2. **Version Everything**: Use proper API versioning
3. **Communicate Changes**: Notify consumers before changes
4. **Test Integration**: Verify changes don't break consumers
5. **Plan Migrations**: Give adequate time for consumer updates

---

## 📚 Learning Path Quick Reference

### Beginner (0-3 months)
- [ ] Understand the four relationship types
- [ ] Document relationships for one service
- [ ] Identify coupling problems in existing code
- [ ] Learn hierarchy rules (System → Feature → Component)
- [ ] Practice with provided exercises

### Intermediate (3-12 months)
- [ ] Lead relationship documentation for a team
- [ ] Design and implement decoupling solutions
- [ ] Organize components into proper hierarchy
- [ ] Plan and execute API version migration
- [ ] Mentor others on relationship best practices

### Advanced (1+ years)
- [ ] Establish organization-wide relationship standards
- [ ] Design tooling for relationship discovery
- [ ] Lead major architectural refactoring initiatives
- [ ] Create training materials and processes
- [ ] Contribute to architectural decision-making

### Expert (2+ years)
- [ ] Research and experiment with new patterns
- [ ] Speak at conferences about architectural practices
- [ ] Contribute to open source architectural tools
- [ ] Mentor other architects and senior developers
- [ ] Influence industry best practices

---

## 🎯 Success Metrics

### Individual Success Indicators
- ✅ Can document any component's relationships completely
- ✅ Can identify and fix coupling problems
- ✅ Can organize components into proper hierarchy
- ✅ Can plan safe API migrations
- ✅ Can onboard new team members quickly using documentation

### Team Success Indicators
- ✅ All services have complete relationship documentation
- ✅ Services can be deployed independently
- ✅ New team members become productive within days, not weeks
- ✅ API changes don't cause surprise breakages
- ✅ System architecture is easy to understand and navigate

### Organization Success Indicators
- ✅ Consistent relationship documentation across all teams
- ✅ Minimal coupling between team boundaries
- ✅ Clear system ownership and boundaries
- ✅ Smooth API evolution without breaking consumers
- ✅ Fast onboarding and knowledge transfer across teams

Remember: These are tools to help you apply the concepts. The real learning happens when you practice these techniques on real systems with real constraints and real teammates!