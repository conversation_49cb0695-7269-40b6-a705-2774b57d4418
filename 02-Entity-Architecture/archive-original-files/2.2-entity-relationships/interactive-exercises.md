# Interactive Learning Exercises: Relationship Best Practices

## Table of Contents

### Exercise Sets
- [Exercise Set 1: Keep Relationships Explicit](#exercise-set-1-keep-relationships-explicit)
  - [Scenario 1.1: The Mystery Service (Basic Level)](#scenario-11-the-mystery-service-basic-level)
  - [Scenario 1.2: The Checkout Flow Investigation (Intermediate Level)](#scenario-12-the-checkout-flow-investigation-intermediate-level)
  - [Scenario 1.3: The Microservices Migration (Advanced Level)](#scenario-13-the-microservices-migration-advanced-level)
- [Exercise Set 2: Minimize Coupling](#exercise-set-2-minimize-coupling)
  - [Scenario 2.1: The Tightly Coupled Services (Basic Level)](#scenario-21-the-tightly-coupled-services-basic-level)
  - [Scenario 2.2: The Library Dependency Problem (Intermediate Level)](#scenario-22-the-library-dependency-problem-intermediate-level)
  - [Scenario 2.3: The Event-Driven Decoupling Challenge (Advanced Level)](#scenario-23-the-event-driven-decoupling-challenge-advanced-level)
- [Exercise Set 3: Maintain Hierarchy Integrity](#exercise-set-3-maintain-hierarchy-integrity)
  - [Scenario 3.1: The Misplaced Components (Basic Level)](#scenario-31-the-misplaced-components-basic-level)
  - [Scenario 3.2: The Organizational Restructure (Intermediate Level)](#scenario-32-the-organizational-restructure-intermediate-level)
  - [Scenario 3.3: The Domain Boundary Decision (Advanced Level)](#scenario-33-the-domain-boundary-decision-advanced-level)
- [Exercise Set 4: Version Your Contracts](#exercise-set-4-version-your-contracts)
  - [Scenario 4.1: The Breaking Change Dilemma (Basic Level)](#scenario-41-the-breaking-change-dilemma-basic-level)
  - [Scenario 4.2: The API Evolution Strategy (Intermediate Level)](#scenario-42-the-api-evolution-strategy-intermediate-level)
  - [Scenario 4.3: The Microservices Contract Management (Advanced Level)](#scenario-43-the-microservices-contract-management-advanced-level)

### Solutions and Learning Materials
- [Solutions and Answer Keys](#solutions-and-answer-keys)
  - [Solution 1.1: The Mystery Service](#solution-11-the-mystery-service)
  - [Solution 1.2: The Checkout Flow Investigation](#solution-12-the-checkout-flow-investigation)
  - [Solution 1.3: The Microservices Migration (Advanced Level)](#solution-13-the-microservices-migration-advanced-level)
  - [Solution 2.1: The Tightly Coupled Services (Basic Level)](#solution-21-the-tightly-coupled-services-basic-level)
  - [Solution 2.2: The Library Dependency Problem (Intermediate Level)](#solution-22-the-library-dependency-problem-intermediate-level)
  - [Solution 3.1: The Misplaced Components (Basic Level)](#solution-31-the-misplaced-components-basic-level)
  - [Solution 4.1: The Breaking Change Dilemma (Basic Level)](#solution-41-the-breaking-change-dilemma-basic-level)

### Assessment and Reference
- [Self-Assessment Questions](#self-assessment-questions)
  - [Explicit Relationships Assessment](#explicit-relationships-assessment)
  - [Coupling Assessment](#coupling-assessment)
  - [Hierarchy Assessment](#hierarchy-assessment)
  - [Versioning Assessment](#versioning-assessment)
- [Quick Reference Cards](#quick-reference-cards)
  - [Relationship Types Quick Reference](#relationship-types-quick-reference)
  - [Common Coupling Problems](#common-coupling-problems)
  - [Hierarchy Rules](#hierarchy-rules)
  - [Versioning Decision Matrix](#versioning-decision-matrix)

### Advanced Learning
- [Learning Reinforcement Questions](#learning-reinforcement-questions)
  - [After Explicit Relationships Exercises](#after-explicit-relationships-exercises)
  - [After Coupling Exercises](#after-coupling-exercises)
  - [After Hierarchy Exercises](#after-hierarchy-exercises)
  - [After Versioning Exercises](#after-versioning-exercises)
- [Advanced Discussion Topics](#advanced-discussion-topics)
  - [Topic 1: Relationship Documentation in Different Architectural Patterns](#topic-1-relationship-documentation-in-different-architectural-patterns)
  - [Topic 2: Coupling vs Performance Trade-offs](#topic-2-coupling-vs-performance-trade-offs)
  - [Topic 3: Hierarchy Evolution Over Time](#topic-3-hierarchy-evolution-over-time)
  - [Topic 4: API Versioning in Different Contexts](#topic-4-api-versioning-in-different-contexts)

### Mastery and Next Steps
- [Mastery Indicators](#mastery-indicators)
  - [Explicit Relationships Mastery](#explicit-relationships-mastery)
  - [Coupling Mastery](#coupling-mastery)
  - [Hierarchy Mastery](#hierarchy-mastery)
  - [Versioning Mastery](#versioning-mastery)
- [Next Steps in Your Learning Journey](#next-steps-in-your-learning-journey)
  - [Beginner → Intermediate](#beginner--intermediate)
  - [Intermediate → Advanced](#intermediate--advanced)
  - [Advanced → Expert](#advanced--expert)

---

## Exercise Set 1: Keep Relationships Explicit

### Scenario 1.1: The Mystery Service (Basic Level)

**Situation**: You've inherited a service called `order-processor` with this minimal documentation:

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: order-processor
  description: Processes customer orders
spec:
  type: service
  lifecycle: production
  owner: ecommerce-team
```

**The Problem**: The service keeps failing with database connection errors and API timeout errors, but you can't tell what it depends on.

**Your Task**: Based on the error logs below, identify what relationships should be documented:

```
ERROR: Connection to database 'inventory_db' failed
ERROR: Timeout calling payment-service API at /api/v1/charge
ERROR: Failed to import validation library 'order-validators'
ERROR: Cannot connect to Redis cache at redis://cache-cluster:6379
```

**Challenge**: Write the explicit relationships that should be added to the catalog-info.yaml file.

**Your Answer**:
```yaml
spec:
  dependsOn:
    # Fill in the dependencies based on the error logs
  
  consumesApis:
    # Fill in the APIs this service calls
```

---

### Scenario 1.2: The Checkout Flow Investigation (Intermediate Level)

**Situation**: A new developer needs to understand the checkout process to fix a bug where orders sometimes get stuck in "processing" status. They found this feature definition:

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: checkout-process
  description: Handles the complete order checkout workflow
spec:
  type: feature
  lifecycle: production
  owner: ecommerce-team
```

**The Problem**: The feature documentation doesn't show what components are involved in checkout, making it impossible to debug the stuck orders.

**Your Task**: You've discovered these services are involved in checkout:
- `cart-service`: Manages shopping cart contents
- `payment-service`: Processes credit card payments  
- `inventory-service`: Checks product availability
- `order-service`: Creates and tracks orders
- `notification-service`: Sends confirmation emails
- `checkout-ui`: The frontend checkout form

**Challenge**: Document the complete checkout feature with explicit relationships showing which components implement it and how they connect.

**Your Answer**:
```yaml
# checkout-process feature
spec:
  implementedBy:
    # List all components that implement this feature

# Then for each service, document their relationships:
# cart-service relationships:
spec:
  dependsOn:
    # What does cart-service depend on?
  consumesApis:
    # What APIs does cart-service call?
  providesApis:
    # What APIs does cart-service provide?
```

---

### Scenario 1.3: The Microservices Migration (Advanced Level)

**Situation**: Your team is breaking apart a monolithic e-commerce application into microservices. You need to document the relationships for the new architecture to avoid the chaos that happened during the last migration.

**The Current Monolith** handles:
- User authentication and profiles
- Product catalog and search
- Shopping cart management
- Order processing and fulfillment
- Payment processing
- Email notifications
- Admin dashboard

**The New Microservices Architecture** will have:
- `auth-service`: User authentication
- `user-profile-service`: User profile management
- `product-catalog-service`: Product information
- `search-service`: Product search functionality
- `cart-service`: Shopping cart operations
- `order-service`: Order processing
- `payment-service`: Payment processing
- `notification-service`: Email/SMS notifications
- `admin-dashboard`: Management interface

**Shared Resources**:
- `postgres-users-db`: User data
- `postgres-products-db`: Product catalog
- `postgres-orders-db`: Order history
- `redis-cache`: Session and cart caching
- `elasticsearch-cluster`: Product search index
- `rabbitmq-events`: Event messaging

**Your Task**: Design the complete relationship model for this microservices architecture. Consider:
- Which services depend on which databases?
- Which services need to communicate with each other?
- What APIs need to be created for service-to-service communication?
- How will the admin dashboard access data from multiple services?

**Challenge**: Create catalog-info.yaml relationship sections for at least 5 of these services, showing realistic dependencies and API relationships.

---

## Exercise Set 2: Minimize Coupling

### Scenario 2.1: The Tightly Coupled Services (Basic Level)

**Situation**: You have two services that are causing deployment headaches because they're too tightly coupled:

```yaml
# user-service
spec:
  dependsOn:
    - component:default/order-service  # Direct database access
    - resource:default/order-database  # Shares order-service's database
```

```yaml
# order-service  
spec:
  dependsOn:
    - component:default/user-service   # Direct method calls
    - resource:default/user-database   # Shares user-service's database
```

**The Problem**: 
- You can't deploy one service without the other
- Changes in user-service break order-service and vice versa
- Both services access each other's databases directly

**Your Task**: Redesign these services to be loosely coupled using proper APIs and separate databases.

**Challenge**: Rewrite the relationships to eliminate tight coupling. Consider:
- What APIs should each service provide?
- How should they communicate instead of direct database access?
- What shared resources might they need?

---

### Scenario 2.2: The Library Dependency Problem (Intermediate Level)

**Situation**: Your team has created a "utility library" that's being used by many services, but it's causing problems:

```yaml
# The problematic utility-library
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: utility-library
  description: Common utilities for all services
spec:
  type: library
  # This library contains:
  # - Database connection utilities
  # - Email sending functions  
  # - Payment processing helpers
  # - User authentication helpers
  # - File upload utilities
  # - Logging configuration
  # - API rate limiting
```

**The Problem**:
- Services that only need logging are forced to include payment processing code
- A change to email functionality requires redeploying all services
- The library has become a "god object" with too many responsibilities

**Your Task**: Break this library into smaller, focused libraries that follow the single responsibility principle.

**Challenge**: 
1. Design 4-6 smaller libraries, each with a single purpose
2. Show how services would depend on only the libraries they actually need
3. Document the relationships for a service that only needs logging and file upload functionality

---

### Scenario 2.3: The Event-Driven Decoupling Challenge (Advanced Level)

**Situation**: You have a tightly coupled order processing system where services directly call each other:

**Current Flow**:
1. `order-service` creates order → directly calls `inventory-service`
2. `inventory-service` reserves items → directly calls `payment-service`  
3. `payment-service` charges card → directly calls `fulfillment-service`
4. `fulfillment-service` ships order → directly calls `notification-service`

**The Problem**:
- If any service is down, the entire flow fails
- Services are tightly coupled and hard to test independently
- Adding new steps to the flow requires changing multiple services

**Your Task**: Redesign this as an event-driven system using message queues to decouple the services.

**Challenge**:
1. Design the event flow using a message queue system
2. Define what events each service publishes and subscribes to
3. Show how the relationships change from direct dependencies to event-based communication
4. Document the catalog-info.yaml for each service in the new architecture

---

## Exercise Set 3: Maintain Hierarchy Integrity

### Scenario 3.1: The Misplaced Components (Basic Level)

**Situation**: A junior developer has created this hierarchy that violates several rules:

```yaml
# System: E-commerce Platform
spec:
  contains:
    - component:default/user-service      # ❌ System contains Component directly
    - feature:default/user-management
    - component:default/payment-gateway   # ❌ System contains Component directly

# Feature: User Management  
spec:
  implementedBy:
    - system:default/authentication-system  # ❌ Feature implemented by System
    - component:default/user-profile-ui
```

**Your Task**: Fix the hierarchy violations by properly organizing these entities.

**Challenge**: Rewrite the relationships following these rules:
- Systems contain Features, not Components
- Features are implemented by Components, not Systems
- Components depend on Resources and other Components

---

### Scenario 3.2: The Organizational Restructure (Intermediate Level)

**Situation**: Your company is reorganizing from functional teams to product teams, and you need to restructure your software architecture to match:

**Old Structure (Functional Teams)**:
- Frontend Team: All UI components
- Backend Team: All services  
- Data Team: All databases

**New Structure (Product Teams)**:
- User Experience Team: Everything related to user accounts and profiles
- Shopping Team: Everything related to browsing and purchasing
- Operations Team: Everything related to order fulfillment and support

**Current Architecture** (needs reorganization):
- `user-registration-service` (Backend Team)
- `user-profile-ui` (Frontend Team)  
- `product-catalog-service` (Backend Team)
- `shopping-cart-ui` (Frontend Team)
- `order-fulfillment-service` (Backend Team)
- `admin-dashboard` (Frontend Team)

**Your Task**: Reorganize these components into proper Systems and Features that align with the new product team structure.

**Challenge**: Create a hierarchy where:
- Each product team owns complete Systems
- Systems contain Features that deliver user value
- Features are implemented by the appropriate Components

---

### Scenario 3.3: The Domain Boundary Decision (Advanced Level)

**Situation**: You're designing the architecture for a new healthcare platform and need to decide how to organize these capabilities:

**Business Capabilities**:
- Patient registration and profiles
- Appointment scheduling
- Medical record management
- Billing and insurance processing
- Prescription management
- Lab result tracking
- Doctor/staff management
- Facility management
- Emergency response coordination

**Technical Components** you need to organize:
- `patient-portal-ui`
- `doctor-dashboard-ui`
- `appointment-service`
- `medical-records-service`
- `billing-service`
- `prescription-service`
- `lab-integration-service`
- `staff-management-service`
- `facility-service`
- `emergency-alert-service`
- `patient-database`
- `medical-records-database`
- `billing-database`

**Your Task**: Design a proper hierarchy that groups related capabilities into Systems and Features.

**Challenge**: 
1. Identify 3-4 major Systems that represent distinct business domains
2. Define Features within each System that deliver specific user value
3. Assign Components to Features based on what they implement
4. Consider which Components might be shared across Features
5. Document the complete hierarchy with proper relationships

---

## Exercise Set 4: Version Your Contracts

### Scenario 4.1: The Breaking Change Dilemma (Basic Level)

**Situation**: You need to update your `user-api` to add required email validation, but you're worried about breaking existing consumers:

**Current API** (v1):
```yaml
POST /api/v1/users
{
  "username": "john_doe",
  "email": "<EMAIL>",    # Optional field
  "password": "secret123"
}
```

**Proposed API** (v2):
```yaml
POST /api/v2/users  
{
  "username": "john_doe",
  "email": "<EMAIL>",    # Now required field
  "password": "secret123",
  "email_verified": false         # New field
}
```

**Your Task**: Plan a safe migration strategy that doesn't break existing consumers.

**Challenge**: 
1. Identify what makes this a breaking change
2. Design a versioning strategy that supports both old and new consumers
3. Create a timeline for deprecating the old version
4. Document how consumers should migrate

---

### Scenario 4.2: The API Evolution Strategy (Intermediate Level)

**Situation**: Your `order-api` has evolved over 2 years and now has multiple versions with different consumers:

**Version History**:
- `v1`: Used by mobile app (can't update frequently)
- `v2`: Used by web app (updates monthly)
- `v3`: Used by internal admin tools (updates weekly)
- `v4`: Proposed new version with major improvements

**Current Consumer Dependencies**:
```yaml
# mobile-app
spec:
  consumesApis:
    - api:default/order-api-v1

# web-app  
spec:
  consumesApis:
    - api:default/order-api-v2

# admin-dashboard
spec:
  consumesApis:
    - api:default/order-api-v3
```

**Your Task**: Create a version management strategy that:
- Introduces v4 safely
- Plans deprecation of older versions
- Minimizes disruption to consumers
- Provides clear migration paths

**Challenge**:
1. Design the versioning timeline
2. Create migration guides for each consumer
3. Document the support lifecycle for each version
4. Plan the technical implementation of supporting multiple versions

---

### Scenario 4.3: The Microservices Contract Management (Advanced Level)

**Situation**: You're managing API contracts across a complex microservices ecosystem with these services and their APIs:

**Services and APIs**:
- `user-service` → provides `user-api` (v1, v2)
- `order-service` → provides `order-api` (v1, v2, v3)
- `payment-service` → provides `payment-api` (v1)
- `notification-service` → provides `notification-api` (v1, v2)

**Consumer Matrix**:
- `web-app`: uses user-api v2, order-api v3, payment-api v1
- `mobile-app`: uses user-api v1, order-api v2, payment-api v1  
- `admin-dashboard`: uses all APIs, latest versions
- `reporting-service`: uses order-api v2, user-api v1
- `third-party-integration`: uses order-api v1 (external partner)

**Upcoming Changes**:
- `user-service` needs to release v3 with GDPR compliance features
- `payment-service` needs v2 with new payment methods
- `order-service` wants to deprecate v1 and v2
- New `analytics-service` needs data from multiple APIs

**Your Task**: Create a comprehensive contract management strategy for this ecosystem.

**Challenge**:
1. Design a versioning policy that works across all services
2. Create a deprecation timeline that considers all consumers
3. Plan the rollout of new API versions
4. Design a contract testing strategy to prevent breaking changes
5. Document the communication plan for API changes
6. Create a dependency matrix showing which consumers need to migrate when

---

## Solutions and Answer Keys

### Solution 1.1: The Mystery Service

**Correct Answer**:
```yaml
spec:
  dependsOn:
    - resource:default/inventory-db
      # Stores product inventory levels and availability
    - component:default/order-validators
      # Library for validating order data and business rules
    - resource:default/redis-cache-cluster
      # Caches frequently accessed order data for performance
  
  consumesApis:
    - api:default/payment-service-api
      # Processes payment transactions for completed orders
```

**Key Learning Points**:
- Error logs are valuable clues for identifying undocumented dependencies
- Database connections, library imports, and API calls should all be documented
- Comments should explain the business purpose, not just repeat the technical details

---

### Solution 1.2: The Checkout Flow Investigation

**Correct Answer**:
```yaml
# checkout-process feature
spec:
  implementedBy:
    - component:default/cart-service
    - component:default/payment-service
    - component:default/inventory-service
    - component:default/order-service
    - component:default/notification-service
    - component:default/checkout-ui

# cart-service relationships
spec:
  dependsOn:
    - resource:default/redis-cart-cache
  providesApis:
    - api:default/cart-api

# payment-service relationships  
spec:
  consumesApis:
    - api:default/cart-api
  providesApis:
    - api:default/payment-api

# inventory-service relationships
spec:
  dependsOn:
    - resource:default/inventory-database
  providesApis:
    - api:default/inventory-api

# order-service relationships
spec:
  dependsOn:
    - resource:default/orders-database
  consumesApis:
    - api:default/cart-api
    - api:default/payment-api
    - api:default/inventory-api
  providesApis:
    - api:default/order-api

# notification-service relationships
spec:
  consumesApis:
    - api:default/order-api
  providesApis:
    - api:default/notification-api

# checkout-ui relationships
spec:
  consumesApis:
    - api:default/cart-api
    - api:default/payment-api
    - api:default/order-api
```

**Key Learning Points**:
- Features should clearly show which components implement them
- Each service should document both what it provides and what it consumes
- The relationship documentation creates a complete picture of the data flow

---

*[Additional solutions would continue for all exercises...]*

## Self-Assessment Questions

After completing the exercises, test your understanding:

### Explicit Relationships Assessment
1. Can you identify all dependencies of a component just by looking at its catalog-info.yaml?
2. Do you understand the difference between `dependsOn`, `consumesApis`, and `providesApis`?
3. Can you explain why explicit relationships prevent "mystery failures"?

### Coupling Assessment  
1. Can you identify when two components are too tightly coupled?
2. Do you know how to use APIs and events to reduce coupling?
3. Can you explain why loose coupling improves maintainability?

### Hierarchy Assessment
1. Do you understand the proper relationship between Systems, Features, and Components?
2. Can you organize components into logical business domains?
3. Do you know how to fix hierarchy violations?

### Versioning Assessment
1. Can you identify what constitutes a breaking API change?
2. Do you know how to plan a safe API migration?
3. Can you create a versioning strategy for multiple consumers?

## Quick Reference Cards

### Relationship Types Quick Reference
- **dependsOn**: "I need this to function"
- **consumesApis**: "I call this API"  
- **providesApis**: "I offer this API"
- **implementedBy**: "These components provide this feature"
- **contains**: "This system includes these features"

### Common Coupling Problems
- ✅ **Good**: Service A calls Service B's API
- ❌ **Bad**: Service A directly accesses Service B's database
- ✅ **Good**: Services communicate through events
- ❌ **Bad**: Services make direct method calls to each other

### Hierarchy Rules
- Systems contain Features (not Components)
- Features are implemented by Components  
- Components depend on Resources and other Components
- Resources are shared infrastructure

### Versioning Decision Matrix
- **Add optional field**: Minor version (v1.1)
- **Add required field**: Major version (v2.0)
- **Remove field**: Major version (v2.0)
- **Change field type**: Major version (v2.0)
- **Fix bug without changing interface**: Patch version (v1.0.1)
#
# Complete Solution Guides with Alternative Approaches

### Solution 1.3: The Microservices Migration (Advanced Level)

**Complete Solution with Alternative Approaches**:

#### Primary Solution: API-First Architecture

```yaml
# auth-service
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: auth-service
spec:
  type: service
  dependsOn:
    - resource:default/postgres-users-db
    - resource:default/redis-cache
  providesApis:
    - api:default/authentication-api
    - api:default/user-session-api

# user-profile-service  
spec:
  dependsOn:
    - resource:default/postgres-users-db
    - resource:default/redis-cache
  consumesApis:
    - api:default/authentication-api
  providesApis:
    - api:default/user-profile-api

# product-catalog-service
spec:
  dependsOn:
    - resource:default/postgres-products-db
    - resource:default/elasticsearch-cluster
  providesApis:
    - api:default/product-catalog-api

# search-service
spec:
  dependsOn:
    - resource:default/elasticsearch-cluster
  consumesApis:
    - api:default/product-catalog-api
  providesApis:
    - api:default/search-api

# cart-service
spec:
  dependsOn:
    - resource:default/redis-cache
  consumesApis:
    - api:default/authentication-api
    - api:default/product-catalog-api
  providesApis:
    - api:default/cart-api

# order-service
spec:
  dependsOn:
    - resource:default/postgres-orders-db
    - resource:default/rabbitmq-events
  consumesApis:
    - api:default/cart-api
    - api:default/user-profile-api
    - api:default/payment-api
  providesApis:
    - api:default/order-api

# payment-service
spec:
  dependsOn:
    - resource:default/postgres-orders-db
  consumesApis:
    - api:default/authentication-api
  providesApis:
    - api:default/payment-api

# notification-service
spec:
  dependsOn:
    - resource:default/rabbitmq-events
  consumesApis:
    - api:default/user-profile-api
  providesApis:
    - api:default/notification-api

# admin-dashboard
spec:
  consumesApis:
    - api:default/user-profile-api
    - api:default/product-catalog-api
    - api:default/order-api
    - api:default/authentication-api
```

#### Alternative Solution: Event-Driven Architecture

For teams that prefer loose coupling through events:

```yaml
# order-service (event-driven version)
spec:
  dependsOn:
    - resource:default/postgres-orders-db
    - resource:default/rabbitmq-events
  consumesApis:
    - api:default/cart-api
  # Publishes events instead of direct API calls
  publishes:
    - event:default/order-created
    - event:default/order-completed
  subscribes:
    - event:default/payment-processed
    - event:default/inventory-reserved

# notification-service (event-driven version)
spec:
  dependsOn:
    - resource:default/rabbitmq-events
  subscribes:
    - event:default/order-created
    - event:default/payment-processed
    - event:default/user-registered
```

#### Discussion: When to Choose Each Approach

**API-First Approach** is better when:
- You need synchronous responses (user waiting for result)
- You have strong consistency requirements
- Your team is familiar with REST APIs
- You need clear request/response contracts

**Event-Driven Approach** is better when:
- You can handle eventual consistency
- You want maximum service independence
- You have high-volume, asynchronous workflows
- You need to support complex business processes

**Key Learning Points**:
- Both approaches can be valid depending on requirements
- You can mix approaches within the same system
- Document your architectural decisions and reasoning
- Consider team expertise when choosing patterns

---

### Solution 2.1: The Tightly Coupled Services (Basic Level)

**Problem Analysis**:
The original design violates several coupling principles:
- Services share databases (data coupling)
- Services have circular dependencies (control coupling)
- Services make direct calls instead of using APIs (interface coupling)

**Correct Solution**:

```yaml
# user-service (decoupled version)
spec:
  dependsOn:
    - resource:default/postgres-users-db  # Own database only
  providesApis:
    - api:default/user-profile-api
    - api:default/user-authentication-api

# order-service (decoupled version)  
spec:
  dependsOn:
    - resource:default/postgres-orders-db  # Own database only
  consumesApis:
    - api:default/user-profile-api  # Uses API instead of direct access
  providesApis:
    - api:default/order-management-api
```

**Alternative Solution: Event-Based Decoupling**

For even looser coupling:

```yaml
# user-service (event-based version)
spec:
  dependsOn:
    - resource:default/postgres-users-db
    - resource:default/event-bus
  providesApis:
    - api:default/user-profile-api
  publishes:
    - event:default/user-created
    - event:default/user-updated

# order-service (event-based version)
spec:
  dependsOn:
    - resource:default/postgres-orders-db
    - resource:default/event-bus
  subscribes:
    - event:default/user-created  # Gets user info via events
  providesApis:
    - api:default/order-management-api
```

**Alternative Solution: Shared Data Service**

For teams that need shared user data:

```yaml
# user-data-service (shared service approach)
spec:
  dependsOn:
    - resource:default/postgres-users-db
  providesApis:
    - api:default/user-data-api

# user-service (business logic only)
spec:
  consumesApis:
    - api:default/user-data-api
  providesApis:
    - api:default/user-profile-api

# order-service (uses shared data service)
spec:
  dependsOn:
    - resource:default/postgres-orders-db
  consumesApis:
    - api:default/user-data-api
  providesApis:
    - api:default/order-management-api
```

**Discussion: Choosing the Right Decoupling Strategy**

**API-Based Decoupling** when:
- You need real-time data consistency
- Services have clear ownership boundaries
- You want simple request/response patterns

**Event-Based Decoupling** when:
- You can accept eventual consistency
- You want maximum service independence
- You have complex business workflows

**Shared Data Service** when:
- Multiple services need the same data
- You want to centralize data access patterns
- You have complex data consistency requirements

**Key Learning Points**:
- Decoupling often requires introducing new components (APIs, events, shared services)
- Each approach has trade-offs in complexity, consistency, and performance
- Start with the simplest solution that meets your requirements
- You can evolve from one pattern to another as needs change

---

### Solution 2.2: The Library Dependency Problem (Intermediate Level)

**Problem Analysis**:
The original "utility-library" violates the Single Responsibility Principle and creates unnecessary coupling. Services that only need logging are forced to include payment processing code.

**Correct Solution: Focused Libraries**

```yaml
# database-connection-library
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: database-connection-library
spec:
  type: library
  # Focused on database connectivity only

# email-service-library
spec:
  type: library
  # Focused on email operations only

# payment-processing-library  
spec:
  type: library
  # Focused on payment operations only

# authentication-library
spec:
  type: library
  # Focused on auth operations only

# file-upload-library
spec:
  type: library
  # Focused on file operations only

# logging-library
spec:
  type: library
  # Focused on logging only

# rate-limiting-library
spec:
  type: library
  # Focused on rate limiting only
```

**Example Service Using Focused Libraries**:

```yaml
# A service that only needs logging and file upload
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: document-processor-service
spec:
  type: service
  dependsOn:
    - component:default/logging-library
      # Only includes logging functionality
    - component:default/file-upload-library
      # Only includes file upload functionality
    # No unnecessary payment or email dependencies!
```

**Alternative Solution: Layered Libraries**

For teams that want some shared functionality:

```yaml
# Core utilities (used by all services)
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: core-utilities-library
spec:
  type: library
  # Contains: logging, configuration, basic validation

# Data access layer (used by services that need databases)
metadata:
  name: data-access-library
spec:
  type: library
  dependsOn:
    - component:default/core-utilities-library
  # Contains: database connections, ORM utilities

# Business logic layer (used by business services)
metadata:
  name: business-logic-library
spec:
  type: library
  dependsOn:
    - component:default/core-utilities-library
  # Contains: validation rules, business calculations

# Integration layer (used by services that call external APIs)
metadata:
  name: integration-library
spec:
  type: library
  dependsOn:
    - component:default/core-utilities-library
  # Contains: HTTP clients, API wrappers, retry logic
```

**Alternative Solution: Plugin Architecture**

For maximum flexibility:

```yaml
# Core framework
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: service-framework-library
spec:
  type: library
  # Minimal core with plugin system

# Individual plugins
metadata:
  name: database-plugin-library
spec:
  type: library
  dependsOn:
    - component:default/service-framework-library

metadata:
  name: email-plugin-library
spec:
  type: library
  dependsOn:
    - component:default/service-framework-library
```

**Discussion: Library Design Strategies**

**Focused Libraries** (recommended for most teams):
- ✅ Services only include what they need
- ✅ Changes to one library don't affect unrelated services
- ✅ Easy to understand and maintain
- ❌ More libraries to manage

**Layered Libraries**:
- ✅ Reduces total number of libraries
- ✅ Enforces architectural patterns
- ❌ Can still create unnecessary dependencies
- ❌ Changes to core layer affect many services

**Plugin Architecture**:
- ✅ Maximum flexibility
- ✅ Services can choose exactly what they need
- ❌ More complex to implement and understand
- ❌ Requires sophisticated framework design

**Key Learning Points**:
- Library design significantly impacts coupling across your system
- Start with focused libraries - they're easier to understand and maintain
- Consider the team's ability to manage multiple libraries
- You can always combine focused libraries later if needed

---

### Solution 3.1: The Misplaced Components (Basic Level)

**Problem Analysis**:
The original hierarchy violates these rules:
- Systems should contain Features, not Components directly
- Features should be implemented by Components, not Systems
- The hierarchy doesn't reflect proper business organization

**Correct Solution**:

```yaml
# System: E-commerce Platform (fixed)
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ecommerce-platform
spec:
  type: system
  contains:
    - feature:default/user-management
    - feature:default/payment-processing
    # Systems contain Features, not Components

# Feature: User Management (fixed)
metadata:
  name: user-management
spec:
  type: feature
  implementedBy:
    - component:default/user-service
    - component:default/user-profile-ui
    # Features implemented by Components, not Systems

# Feature: Payment Processing (new - properly organized)
metadata:
  name: payment-processing
spec:
  type: feature
  implementedBy:
    - component:default/payment-gateway
    # Component now properly belongs to a Feature

# Components (now properly placed)
metadata:
  name: user-service
spec:
  type: service
  # Component details...

metadata:
  name: user-profile-ui
spec:
  type: website
  # Component details...

metadata:
  name: payment-gateway
spec:
  type: service
  # Component details...
```

**Alternative Solution: More Granular Features**

For teams that want more specific feature organization:

```yaml
# System: E-commerce Platform
spec:
  contains:
    - feature:default/user-registration
    - feature:default/user-profile-management
    - feature:default/payment-processing
    - feature:default/payment-gateway-integration

# Feature: User Registration
metadata:
  name: user-registration
spec:
  implementedBy:
    - component:default/user-registration-service
    - component:default/registration-ui

# Feature: User Profile Management
metadata:
  name: user-profile-management
spec:
  implementedBy:
    - component:default/user-profile-service
    - component:default/profile-ui
```

**Discussion: Granularity Decisions**

**Coarse-Grained Features** (fewer, broader features):
- ✅ Simpler hierarchy to understand
- ✅ Fewer entities to manage
- ❌ Features might become too broad
- ❌ Less precise ownership boundaries

**Fine-Grained Features** (more, specific features):
- ✅ Clear, focused feature boundaries
- ✅ Precise ownership and responsibility
- ❌ More complex hierarchy
- ❌ More entities to manage

**Key Learning Points**:
- Hierarchy rules are non-negotiable: Systems → Features → Components
- Feature granularity should match your team's organizational structure
- Features should represent user-visible capabilities
- Components should have clear technical responsibilities

---

### Solution 4.1: The Breaking Change Dilemma (Basic Level)

**Problem Analysis**:
Making the email field required is a breaking change because existing consumers might not be sending email in their requests.

**Correct Solution: Gradual Migration Strategy**

**Phase 1: Introduce v2 alongside v1**
```yaml
# API v1 (keep existing behavior)
POST /api/v1/users
{
  "username": "john_doe",
  "email": "<EMAIL>",    # Still optional
  "password": "secret123"
}

# API v2 (new requirements)
POST /api/v2/users
{
  "username": "john_doe", 
  "email": "<EMAIL>",    # Now required
  "password": "secret123",
  "email_verified": false         # New field
}
```

**Phase 2: Migration Timeline**
```yaml
# Month 1-2: Dual support
- Deploy v2 API alongside v1
- Update documentation
- Notify consumers about v2 availability

# Month 3-4: Consumer migration
- Provide migration guides
- Offer support for consumer updates
- Monitor v1 usage metrics

# Month 5-6: Deprecation warnings
- Add deprecation headers to v1 responses
- Send notifications to remaining v1 consumers
- Set firm end-of-life date

# Month 7+: v1 removal
- Remove v1 endpoints
- Monitor for any remaining v1 calls
- Provide emergency support if needed
```

**Alternative Solution: Backward-Compatible Evolution**

For teams that want to avoid versioning:

```yaml
# Single API with backward compatibility
POST /api/v1/users
{
  "username": "john_doe",
  "email": "<EMAIL>",    # Optional but validated if provided
  "password": "secret123",
  "email_verified": false         # Optional, defaults to false
}

# Server behavior:
# - If email provided: validate format and set email_verified: false
# - If email missing: create user but require email verification later
# - Add new field without breaking existing consumers
```

**Alternative Solution: Feature Flags**

For gradual rollout:

```yaml
# Same API endpoint with feature flag control
POST /api/v1/users
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "secret123"
}

# Server uses feature flags to control validation:
# - Flag "strict_email_validation": controls whether email is required
# - Flag "email_verification_flow": controls new verification behavior
# - Gradually enable flags for different consumer groups
```

**Discussion: Migration Strategy Trade-offs**

**API Versioning**:
- ✅ Clear contract boundaries
- ✅ Consumers can migrate at their own pace
- ❌ More complex to maintain multiple versions
- ❌ Infrastructure overhead

**Backward-Compatible Evolution**:
- ✅ No versioning complexity
- ✅ Single API to maintain
- ❌ API becomes more complex over time
- ❌ Harder to make truly breaking changes

**Feature Flags**:
- ✅ Gradual rollout control
- ✅ Easy rollback if problems occur
- ❌ Code complexity with flag logic
- ❌ Temporary solution, still need migration plan

**Key Learning Points**:
- Always identify breaking changes before implementing them
- Plan migration timelines that respect consumer constraints
- Communicate changes early and provide clear migration paths
- Consider the long-term maintenance cost of your chosen approach

---

## Learning Reinforcement Questions

### After Explicit Relationships Exercises

**Reflection Questions**:
1. **Understanding Check**: Can you explain why implicit relationships cause "mystery failures" in your own words?
2. **Application**: Think of a service in your current project. What relationships might be undocumented?
3. **Problem-Solving**: If you inherited a service with no relationship documentation, what steps would you take to discover its dependencies?

**Self-Assessment Scenarios**:
- **Scenario**: You're about to deploy a library update. How would explicit relationships help you identify which services might be affected?
- **Challenge**: Design a process for your team to ensure new services always have explicit relationships documented.

### After Coupling Exercises

**Reflection Questions**:
1. **Concept Mastery**: What's the difference between "tight coupling" and "loose coupling"? Give examples from your own experience.
2. **Design Thinking**: When would you choose API-based decoupling vs event-based decoupling?
3. **Trade-off Analysis**: What are the costs and benefits of breaking a large library into smaller, focused libraries?

**Self-Assessment Scenarios**:
- **Scenario**: Two services in your system can't be deployed independently. What coupling problems might be causing this?
- **Challenge**: Design a decoupling strategy for a monolithic application you're familiar with.

### After Hierarchy Exercises

**Reflection Questions**:
1. **Rule Application**: Can you state the hierarchy rules (Systems → Features → Components) and explain why each rule exists?
2. **Organizational Alignment**: How should your software hierarchy reflect your team's organizational structure?
3. **Boundary Decisions**: When should you create a new System vs adding a Feature to an existing System?

**Self-Assessment Scenarios**:
- **Scenario**: Your team is reorganizing from functional teams to product teams. How would this affect your software hierarchy?
- **Challenge**: Design a hierarchy for a domain you're familiar with (e.g., healthcare, finance, education).

### After Versioning Exercises

**Reflection Questions**:
1. **Change Classification**: Can you identify what makes an API change "breaking" vs "non-breaking"?
2. **Migration Planning**: What factors should influence your API deprecation timeline?
3. **Consumer Impact**: How do you balance innovation (new API features) with stability (not breaking existing consumers)?

**Self-Assessment Scenarios**:
- **Scenario**: You need to remove a field from your API that you discovered is never used. Is this a breaking change? How would you handle it?
- **Challenge**: Create a versioning policy for your team that balances innovation with stability.

## Advanced Discussion Topics

### Topic 1: Relationship Documentation in Different Architectural Patterns

**Discussion Prompt**: How do relationship documentation needs differ between:
- Monolithic applications
- Microservices architectures  
- Event-driven systems
- Serverless functions

**Key Points to Consider**:
- Granularity of documentation
- Types of relationships that matter most
- Tools and automation possibilities
- Maintenance overhead

### Topic 2: Coupling vs Performance Trade-offs

**Discussion Prompt**: Sometimes loose coupling can hurt performance (e.g., network calls vs in-memory calls). How do you balance architectural purity with performance requirements?

**Scenarios to Explore**:
- High-frequency trading systems
- Real-time gaming applications
- Mobile applications with limited bandwidth
- Embedded systems with resource constraints

### Topic 3: Hierarchy Evolution Over Time

**Discussion Prompt**: How should your software hierarchy evolve as your organization grows from startup to enterprise?

**Evolution Stages**:
- Single team, single product
- Multiple teams, single product
- Multiple teams, multiple products
- Multiple business units, multiple products

### Topic 4: API Versioning in Different Contexts

**Discussion Prompt**: How do versioning strategies differ for:
- Internal APIs (within your organization)
- Partner APIs (B2B integrations)
- Public APIs (external developers)
- Mobile app APIs (slow update cycles)

**Considerations**:
- Control over consumers
- Update frequency capabilities
- Backward compatibility requirements
- Support lifecycle expectations

## Mastery Indicators

You've mastered these concepts when you can:

### Explicit Relationships Mastery
- ✅ Look at any component and immediately identify what it depends on
- ✅ Predict the impact of changes before making them
- ✅ Onboard new team members quickly using relationship documentation
- ✅ Debug system failures by following relationship chains

### Coupling Mastery
- ✅ Identify tight coupling problems in existing systems
- ✅ Choose appropriate decoupling strategies for different scenarios
- ✅ Design new systems with appropriate coupling levels
- ✅ Refactor tightly coupled code without breaking functionality

### Hierarchy Mastery
- ✅ Organize any set of components into proper Systems and Features
- ✅ Align software hierarchy with organizational structure
- ✅ Make boundary decisions that support team autonomy
- ✅ Evolve hierarchy as the organization grows

### Versioning Mastery
- ✅ Identify breaking changes before implementing them
- ✅ Plan migration strategies that respect consumer constraints
- ✅ Communicate API changes effectively to all stakeholders
- ✅ Balance innovation with stability in API evolution

## Next Steps in Your Learning Journey

### Beginner → Intermediate
- Practice documenting relationships for existing systems
- Identify and fix one coupling problem in your current project
- Organize your team's components into proper hierarchy
- Plan and execute one API version migration

### Intermediate → Advanced
- Design relationship documentation standards for your organization
- Lead a decoupling initiative across multiple teams
- Establish hierarchy governance processes
- Create API versioning policies and tooling

### Advanced → Expert
- Mentor others in relationship best practices
- Contribute to tooling that automates relationship discovery
- Research and experiment with new architectural patterns
- Share knowledge through talks, writing, or open source contributions

Remember: Mastery comes through practice. Start with small improvements in your current projects and gradually take on larger architectural challenges as your confidence grows.

## Quick Reference Cards

These quick reference cards provide immediate practical guidance for applying relationship best practices in your daily work.

### 🎯 Relationship Types Quick Reference

| Relationship | When to Use | Example |
|-------------|-------------|---------|
| `dependsOn` | "I need this to function" | Service → Database |
| `consumesApis` | "I call this API" | Web App → User API |
| `providesApis` | "I offer this API" | Auth Service → Auth API |
| `implementedBy` | "These components provide this feature" | User Management → User Service |
| `contains` | "This system includes these features" | E-commerce → Shopping Cart |

### 🔗 Explicit Relationships Checklist

**✅ Before You Deploy**
- [ ] All database dependencies documented in `dependsOn`
- [ ] All library dependencies documented in `dependsOn`
- [ ] All API calls documented in `consumesApis`
- [ ] All exposed APIs documented in `providesApis`
- [ ] All relationships have explanatory comments
- [ ] No "mystery" dependencies that aren't documented

### 🔄 Coupling Assessment Quick Card

| 🚨 Red Flags | 🟡 Yellow Flags | ✅ Green Flags |
|-------------|----------------|---------------|
| Services share databases | Services call each other directly | Services use APIs only |
| Circular dependencies | Large shared libraries | Focused, single-purpose libraries |
| Can't deploy independently | Tight release coordination needed | Independent deployment possible |
| Changes break other services | Changes require extensive testing | Changes have predictable impact |

### 🏗️ Hierarchy Rules Quick Reference

**The Golden Rules**
```
System (Business Domain)
  └── contains → Feature (User Capability)
        └── implementedBy → Component (Technical Implementation)
              └── dependsOn → Resource (Infrastructure)
```

**Hierarchy Validation Checklist**
- [ ] Systems contain Features (not Components directly)
- [ ] Features are implemented by Components (not Systems)
- [ ] Components depend on Resources and other Components
- [ ] No circular containment (A contains B, B contains A)
- [ ] Each entity has a clear owner and purpose

### 🚀 API Versioning Quick Decision Guide

| Change Type | Breaking? | Version Impact |
|------------|-----------|----------------|
| Add optional field | ❌ No | Minor (v1.1) |
| Add required field | ✅ Yes | Major (v2.0) |
| Remove any field | ✅ Yes | Major (v2.0) |
| Change field type | ✅ Yes | Major (v2.0) |
| Fix bug, same interface | ❌ No | Patch (v1.0.1) |

### 📋 Common Pitfalls Prevention Checklist

**Explicit Relationships Pitfalls**
- [ ] **Avoid**: Documenting only some relationships → **Fix**: Document ALL dependencies, APIs, and resources
- [ ] **Avoid**: Using vague names like "database" → **Fix**: Use specific names like "postgres-user-profiles-db"
- [ ] **Avoid**: No comments explaining business purpose → **Fix**: Add "Why" comments for every relationship

**Coupling Pitfalls**
- [ ] **Avoid**: Services sharing databases → **Fix**: Each service owns its data, uses APIs to access others
- [ ] **Avoid**: Creating "god" libraries with everything → **Fix**: Split into focused, single-purpose libraries
- [ ] **Avoid**: Direct service-to-service method calls → **Fix**: Use REST APIs or event-driven communication

**Hierarchy Pitfalls**
- [ ] **Avoid**: Systems containing Components directly → **Fix**: Systems contain Features, Features are implemented by Components
- [ ] **Avoid**: Features implemented by Systems → **Fix**: Features are implemented by Components
- [ ] **Avoid**: Organizing by technology instead of business value → **Fix**: Group by user-facing capabilities and business domains

### 🛠️ Emergency Troubleshooting Cards

**"My Service Keeps Failing" Debug Card**
1. **Check Dependencies**: Are all `dependsOn` components running?
2. **Verify APIs**: Are all `consumesApis` endpoints responding?
3. **Test Resources**: Can you connect to all databases/queues?
4. **Review Recent Changes**: Did any dependencies change recently?
5. **Check Versions**: Are you using compatible API versions?

**"I Don't Know What Will Break" Impact Card**
1. **Find Consumers**: Who uses your `providesApis`?
2. **Check Dependents**: What has you in their `dependsOn`?
3. **Review Relationships**: What other services call your APIs?
4. **Test Scenarios**: Create test cases for each relationship
5. **Plan Rollback**: Have a quick rollback strategy ready

### 📚 Learning Path Quick Reference

**Beginner (0-3 months)**
- [ ] Understand the four relationship types
- [ ] Document relationships for one service
- [ ] Identify coupling problems in existing code
- [ ] Learn hierarchy rules (System → Feature → Component)

**Intermediate (3-12 months)**
- [ ] Lead relationship documentation for a team
- [ ] Design and implement decoupling solutions
- [ ] Organize components into proper hierarchy
- [ ] Plan and execute API version migration

**Advanced (1+ years)**
- [ ] Establish organization-wide relationship standards
- [ ] Design tooling for relationship discovery
- [ ] Lead major architectural refactoring initiatives
- [ ] Create training materials and processes

## Common Patterns

### The Microservices Pattern
```
System: E-Commerce Platform
├── Feature: Product Catalog
│   ├── Service: catalog-service
│   ├── API: catalog-api
│   └── Resource: product-database
├── Feature: Shopping Cart
│   ├── Service: cart-service
│   ├── Library: cart-calculations
│   └── Resource: redis-cache
└── Feature: Checkout
    ├── Service: checkout-service
    ├── Website: checkout-ui
    └── API: payment-api
```

### The Monolithic Pattern
```
System: Legacy Application
├── Feature: User Management
│   └── Service: monolith-app (partial)
├── Feature: Reporting
│   └── Service: monolith-app (partial)
└── Shared Resources
    ├── Resource: main-database
    └── Resource: file-storage
```

### The Library-Centric Pattern
```
System: Data Processing Platform
├── Feature: Data Ingestion
│   ├── Service: ingestion-service
│   └── Library: data-validators
├── Feature: Data Transformation
│   ├── Service: transform-service
│   ├── Library: transform-rules
│   └── Library: data-validators (shared)
└── Feature: Data Export
    ├── Service: export-service
    └── Library: format-converters
```

## Entity Lifecycle Considerations

### Creation Guidelines
- Start with Systems and Features (business value)
- Add Components as implementation details
- Define APIs before implementing services
- Document Resources before depending on them

### Evolution Patterns
- Features may split as they grow complex
- Services may decompose into microservices
- Libraries may graduate to services
- Resources may consolidate or distribute

### Deprecation Strategies
- Mark entities as deprecated in lifecycle field
- Document migration paths for dependents
- Maintain deprecated entities until all consumers migrate
- Archive rather than delete for historical reference

## Platform-Agnostic Benefits

This entity model provides several critical advantages:

1. **Technology Independence:** Describes relationships independent of specific platforms
2. **Complete Traceability:** Every connection from business to code is explicit
3. **Impact Analysis:** Changes can be traced through the relationship graph
4. **Onboarding Acceleration:** New team members understand both business and technical context
5. **AI Navigation:** Machine-readable relationships enable intelligent automation

## Enhanced Advanced Discussion Topics

### Topic 1: Relationship Documentation in Different Architectural Patterns

**Discussion Prompt**: How do relationship documentation needs differ between:
- Monolithic applications
- Microservices architectures
- Event-driven systems
- Serverless functions

**Key Points to Consider**:
- Granularity of documentation
- Types of relationships that matter most
- Tools and automation possibilities
- Maintenance overhead

### Topic 2: Coupling vs Performance Trade-offs

**Discussion Prompt**: Sometimes loose coupling can hurt performance (e.g., network calls vs in-memory calls). How do you balance architectural purity with performance requirements?

**Scenarios to Explore**:
- High-frequency trading systems
- Real-time gaming applications
- Mobile applications with limited bandwidth
- Embedded systems with resource constraints

### Topic 3: Hierarchy Evolution Over Time

**Discussion Prompt**: How should your software hierarchy evolve as your organization grows from startup to enterprise?

**Evolution Stages**:
- Single team, single product
- Multiple teams, single product
- Multiple teams, multiple products
- Multiple business units, multiple products

### Topic 4: API Versioning in Different Contexts

**Discussion Prompt**: How do versioning strategies differ for:
- Internal APIs (within your organization)
- Partner APIs (B2B integrations)
- Public APIs (external developers)
- Mobile app APIs (slow update cycles)

**Considerations**:
- Control over consumers
- Update frequency capabilities
- Backward compatibility requirements
- Support lifecycle expectations