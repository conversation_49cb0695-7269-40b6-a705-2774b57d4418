# 1. Keep Relationships Explicit

#### What This Means: Making Connections Visible

Imagine you're looking at the blueprint for a new hospital building. A good blueprint doesn't just show you where the rooms are—it shows you how everything connects. You can see where the electrical wires run, how the plumbing connects between floors, which departments share ventilation systems, and how the emergency power backup reaches critical areas.

Now imagine a blueprint that only shows the rooms but none of the connections. You'd see the operating room and the power plant, but you wouldn't know how electricity gets from one to the other. If something breaks, you'd have no idea what else might be affected. That's exactly what happens in software when relationships are **implicit** (hidden) instead of **explicit** (clearly documented).

**Explicit relationships** means documenting every connection between your software components, just like a building blueprint shows every wire and pipe. When relationships are explicit, anyone can look at your system and immediately understand:
- What depends on what
- How changes in one component might affect others
- Why certain components exist and what they support
- How to safely modify or replace components

#### Key Terms Made Simple

**Dependency:** When one piece of software needs another piece to work properly. Like how a heart monitor depends on electricity—without power, it can't function.

**Coupling:** How tightly connected two pieces of software are. Think of it like furniture:
- **Tight coupling** = Built-in cabinets that are permanently attached to the wall
- **Loose coupling** = Modular furniture that can be easily moved or replaced

**Interface:** The agreed-upon way that two pieces of software communicate, like a standardized electrical outlet that any compatible device can plug into.

#### The Building Blueprint Analogy

Let's use a hospital construction project to understand explicit vs implicit relationships:

**Implicit Relationships (The Problem):**
```
❌ Blueprint shows: "Operating Room" and "Power Plant"
❌ Missing information: How does electricity get to the operating room?
❌ Result: Electricians have to guess, leading to:
   - Wasted time figuring out connections
   - Mistakes that cause power outages
   - Inability to plan maintenance
   - New workers can't understand the system
```

**Explicit Relationships (The Solution):**
```
✅ Blueprint shows: "Operating Room" connected to "Emergency Power Circuit #3"
✅ Clear information: Exact path from power plant to operating room
✅ Result: Everyone can see:
   - Which rooms share power circuits
   - What happens if a circuit fails
   - How to safely perform maintenance
   - Where to add new equipment
```

#### Why This Matters: Real-World Consequences

**When relationships are implicit (hidden):**
- **"Mystery Failures":** Something breaks and nobody knows why other things stopped working
- **"Afraid to Change Anything":** Developers avoid making improvements because they don't know what might break
- **"Slow Onboarding":** New team members spend weeks trying to understand how things connect
- **"Duplicate Work":** Teams build the same functionality because they don't know it already exists

**When relationships are explicit (documented):**
- **"Predictable Impact":** You know exactly what will be affected by any change
- **"Confident Development":** Teams can make improvements without fear of breaking things
- **"Fast Onboarding":** New developers understand the system in days, not months
- **"Efficient Reuse":** Teams easily find and reuse existing functionality

#### The Hospital Emergency Example

Consider what happens during a hospital emergency when relationships are clear vs unclear:

**Scenario: Power outage during surgery**

**With Implicit Relationships:**
```
❌ Chaos: "Which backup generator serves the operating room?"
❌ Panic: "Is the heart monitor on the same circuit as the lights?"
❌ Delays: Staff run around checking connections manually
❌ Risk: Patient safety compromised while figuring out power routing
```

**With Explicit Relationships:**
```
✅ Clarity: Emergency chart shows "OR-3 → Emergency Circuit #2 → Generator B"
✅ Speed: Staff immediately know which backup systems are active
✅ Confidence: Everyone knows exactly what equipment is protected
✅ Safety: Patient care continues without interruption
```

This same principle applies to software systems. When your code has explicit relationships, your team can respond to problems quickly and confidently, just like a well-prepared hospital staff.

#### How to Do It: Step-by-Step Implementation Guide

Making relationships explicit in your software architecture involves documenting all connections in your `catalog-info.yaml` files. Here's how to do it systematically:

##### Step 1: Identify All Dependencies

Before you can document relationships, you need to find them. Ask these questions about each component:

1. **What does this component need to function?**
   - Other services it calls
   - Libraries it imports
   - Databases it reads from
   - External APIs it consumes

2. **What does this component provide to others?**
   - APIs it exposes
   - Data it produces
   - Services it offers

3. **What infrastructure does this component require?**
   - Databases
   - Message queues
   - File storage
   - Monitoring systems

##### Step 2: Document Dependencies in catalog-info.yaml

Use the standard relationship fields to make all connections explicit:

```yaml
# For components that depend on other components or resources
spec:
  dependsOn:
    - component:default/auth-service      # This component needs the auth service
    - resource:default/user-database      # This component needs the user database
    - component:default/email-library     # This component uses the email library

# For services that provide APIs
spec:
  providesApis:
    - api:default/user-management-api     # This service exposes this API

# For components that consume APIs
spec:
  consumesApis:
    - api:default/payment-api             # This component calls the payment API
    - api:default/notification-api        # This component calls the notification API
```

##### Step 3: Use Descriptive Names and Annotations

Make your relationships self-documenting with clear names and helpful annotations:

```yaml
spec:
  dependsOn:
    - component:default/user-auth-service
      # Why: Validates user tokens for all protected endpoints
    - resource:default/postgres-user-db
      # Why: Stores user profiles and preferences
    - component:default/email-notification-library
      # Why: Sends welcome emails and password reset notifications
```

#### Before and After Examples

Let's see how explicit relationships transform unclear code into self-documenting architecture:

##### Example 1: User Registration Service

**❌ Before: Implicit Relationships (Confusing)**
```yaml
# catalog-info.yaml for user-registration-service
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-registration-service
  description: Handles user registration
spec:
  type: service
  lifecycle: production
  owner: user-team
  # No relationships documented - dependencies are hidden!
```

**✅ After: Explicit Relationships (Clear)**
```yaml
# catalog-info.yaml for user-registration-service
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: user-registration-service
  description: Handles new user account creation and validation
spec:
  type: service
  lifecycle: production
  owner: user-team
  
  # Clearly document what this service depends on
  dependsOn:
    - component:default/email-validation-library
      # Validates email format and checks for disposable email providers
    - component:default/password-strength-library  
      # Enforces password complexity requirements
    - resource:default/postgres-users-db
      # Stores new user account information
    - resource:default/redis-session-cache
      # Manages temporary registration tokens
  
  # Document what APIs this service consumes
  consumesApis:
    - api:default/email-service-api
      # Sends welcome emails to new users
    - api:default/audit-logging-api
      # Records registration events for compliance
  
  # Document what APIs this service provides
  providesApis:
    - api:default/user-registration-api
      # Allows other services to create user accounts
```

**What changed and why:**
- **dependsOn**: Shows exactly what this service needs to function
- **consumesApis**: Documents external services this component calls
- **providesApis**: Shows what this service offers to others
- **Comments**: Explain the business reason for each relationship

##### Example 2: Shopping Cart Feature

**❌ Before: Implicit Relationships (Mystery Dependencies)**
```yaml
# Feature with hidden implementation details
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: shopping-cart
  description: Shopping cart functionality
spec:
  type: feature
  lifecycle: production
  owner: ecommerce-team
```

**✅ After: Explicit Relationships (Complete Picture)**
```yaml
# Feature with clear implementation relationships
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: shopping-cart
  description: Allows users to add, remove, and manage items before purchase
spec:
  type: feature
  lifecycle: production
  owner: ecommerce-team
  
  # Show exactly which components implement this feature
  implementedBy:
    - component:default/cart-service
      # Backend service that manages cart state and business logic
    - component:default/cart-ui-component
      # Frontend React component for cart display and interaction
    - component:default/cart-persistence-library
      # Handles saving/loading cart data across sessions
  
  # Document the APIs this feature exposes to other features
  providesApis:
    - api:default/shopping-cart-api
      # Allows other features to add items to user carts
```

##### Example 3: Database Resource

**❌ Before: Implicit Usage (Who Uses This?)**
```yaml
# Resource with no usage information
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: user-database
  description: PostgreSQL database
spec:
  type: database
  lifecycle: production
  owner: platform-team
```

**✅ After: Explicit Usage (Clear Dependencies)**
```yaml
# Resource with documented consumers
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: user-database
  description: PostgreSQL database storing user accounts, profiles, and preferences
spec:
  type: database
  lifecycle: production
  owner: platform-team
  
  # Document which components depend on this resource
  # (This is typically managed automatically, but shown for clarity)
  consumedBy:
    - component:default/user-registration-service
      # Stores new user account data
    - component:default/user-profile-service
      # Manages user profile information and preferences
    - component:default/authentication-service
      # Validates user credentials during login
```

#### Configuration Choice Explanations

Each YAML configuration choice serves a specific purpose:

**`dependsOn`**: Use this when your component cannot function without another component or resource
- **Example**: A user service depends on a user database
- **Why**: Makes it clear what needs to be running for this component to work

**`consumesApis`**: Use this when your component makes calls to another service's API
- **Example**: A notification service consumes an email API
- **Why**: Shows the communication flow between services

**`providesApis`**: Use this when your component exposes an API that others can use
- **Example**: An authentication service provides a login API
- **Why**: Makes it clear what functionality this component offers

**`implementedBy`**: Use this for features to show which components provide the functionality
- **Example**: A "user management" feature is implemented by user-service and user-ui components
- **Why**: Connects business capabilities to technical implementation

**Comments in YAML**: Always explain the business reason for each relationship
- **Format**: `# Why: [business reason]`
- **Purpose**: Helps future developers understand not just what connects to what, but why

#### Real-World Problems Caused by Implicit Relationships

Understanding what goes wrong when relationships are hidden helps you appreciate why explicit documentation is so important. Here are real problems that teams face:

##### Problem 1: The "Cascade Failure Mystery"

**Situation**: A team updates a shared library, and suddenly three different services start failing in production.

**What Happened**: The library change was breaking, but nobody knew which services depended on it because the relationships weren't documented.

**Impact**:
- 2 hours of downtime while teams scrambled to identify affected services
- Customer complaints and lost revenue
- Emergency rollback without understanding the full scope
- Team stress and loss of confidence in deployment process

**How Explicit Relationships Would Have Helped**:
```yaml
# If the library had documented its consumers:
spec:
  consumedBy:
    - component:default/user-service
    - component:default/order-service  
    - component:default/notification-service
```
The team would have known exactly which services to test before deploying the library change.

##### Problem 2: The "Onboarding Nightmare"

**Situation**: A new developer joins the team and needs to fix a bug in the checkout process.

**What Happened**: Without documented relationships, the developer spent 3 weeks trying to understand which services were involved in checkout.

**Impact**:
- Simple bug fix took a month instead of a day
- New developer became frustrated and considered leaving
- Senior developers constantly interrupted to explain system connections
- Bug remained unfixed, affecting customer experience

**How Explicit Relationships Would Have Helped**:
```yaml
# Checkout feature with clear implementation
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: checkout-process
spec:
  implementedBy:
    - component:default/checkout-service
    - component:default/payment-service
    - component:default/inventory-service
    - component:default/checkout-ui
```
The new developer would have immediately known which components to examine.

##### Problem 3: The "Accidental Deletion Disaster"

**Situation**: A team deleted what they thought was an unused database, causing multiple services to fail.

**What Happened**: The database wasn't explicitly linked to the services that used it, so it appeared unused in their monitoring tools.

**Impact**:
- 4 hours of complete service outage
- Data recovery from backups required
- Customer trust damaged
- Post-mortem revealed poor documentation practices

**How Explicit Relationships Would Have Helped**:
```yaml
# Each service would have documented its database dependency
spec:
  dependsOn:
    - resource:default/customer-preferences-db
```
The monitoring tools would have shown the database as actively used.

#### Troubleshooting Guide for Relationship Documentation Issues

When you encounter problems with relationship documentation, use this systematic approach:

##### Issue: "I don't know what depends on my component"

**Symptoms**:
- Afraid to make changes because you don't know what might break
- No clear list of consumers for your API or service
- Difficulty planning deprecation or migration

**Diagnosis Steps**:
1. Search your codebase for references to your component name
2. Check API logs to see what services call your endpoints
3. Look for import statements that reference your libraries
4. Review deployment configurations for service dependencies

**Solution**:
```yaml
# Document all known consumers in your component
spec:
  consumedBy:
    - component:default/service-that-calls-me
    - component:default/another-dependent-service
  
  # Or if you provide APIs, list them explicitly
  providesApis:
    - api:default/my-service-api
```

##### Issue: "My component keeps failing and I don't know why"

**Symptoms**:
- Service starts but immediately crashes
- Intermittent failures with unclear causes
- Error messages about missing connections or timeouts

**Diagnosis Steps**:
1. Check if all your documented dependencies are actually running
2. Verify that dependency versions match what you expect
3. Test each dependency connection individually
4. Review recent changes to any dependent services

**Solution**:
```yaml
# Make sure all dependencies are explicitly documented
spec:
  dependsOn:
    - component:default/auth-service
      # Add version requirements if needed
    - resource:default/user-database
    - component:default/logging-library
```

##### Issue: "Changes in other teams break my service"

**Symptoms**:
- Your service fails after other teams deploy changes
- No advance warning about breaking changes
- Difficulty coordinating releases across teams

**Diagnosis Steps**:
1. Identify which external services your component calls
2. Check if those services have documented their APIs
3. Verify if you're consuming APIs correctly according to their contracts
4. Look for version mismatches or deprecated API usage

**Solution**:
```yaml
# Document all external APIs you consume
spec:
  consumesApis:
    - api:default/payment-processing-api
    - api:default/user-authentication-api
    
# And make sure those APIs are properly versioned
```

#### Common Mistakes and How to Fix Them

##### Mistake 1: Documenting Only Some Relationships

**❌ What People Do Wrong**:
```yaml
spec:
  dependsOn:
    - resource:default/main-database
  # Missing: API dependencies, library dependencies, other services
```

**✅ The Right Way**:
```yaml
spec:
  dependsOn:
    - resource:default/main-database          # Database dependency
    - component:default/auth-service          # Service dependency  
    - component:default/validation-library    # Library dependency
  consumesApis:
    - api:default/notification-api            # External API calls
```

**Why This Matters**: Partial documentation is almost as bad as no documentation because it gives false confidence.

##### Mistake 2: Using Vague or Generic Names

**❌ What People Do Wrong**:
```yaml
spec:
  dependsOn:
    - resource:default/database
    - component:default/service
    - component:default/library
```

**✅ The Right Way**:
```yaml
spec:
  dependsOn:
    - resource:default/postgres-user-profiles-db
    - component:default/email-notification-service
    - component:default/password-validation-library
```

**Why This Matters**: Specific names make it immediately clear what each dependency does.

##### Mistake 3: No Comments Explaining Why

**❌ What People Do Wrong**:
```yaml
spec:
  dependsOn:
    - component:default/redis-cache
    - component:default/elasticsearch-service
```

**✅ The Right Way**:
```yaml
spec:
  dependsOn:
    - component:default/redis-cache
      # Caches user session data to improve login performance
    - component:default/elasticsearch-service
      # Provides full-text search functionality for product catalog
```

**Why This Matters**: Comments explain the business reason, helping future developers understand the purpose.

##### Mistake 4: Forgetting to Update Relationships When Code Changes

**❌ What Happens**: Code evolves but catalog-info.yaml stays the same, leading to outdated documentation.

**✅ The Right Process**:
1. When adding a new dependency to your code, immediately update catalog-info.yaml
2. When removing a dependency from your code, remove it from catalog-info.yaml
3. Include relationship updates in your code review process
4. Set up automated checks to detect undocumented dependencies

##### Mistake 5: Circular Dependencies Without Acknowledgment

**❌ What People Do Wrong**: Create circular dependencies and don't document or address them.

**✅ The Right Way**:
```yaml
# Service A
spec:
  dependsOn:
    - component:default/service-b
      # CIRCULAR DEPENDENCY: Service B also depends on us
      # TODO: Refactor to remove circular dependency by extracting shared logic
```

**Why This Matters**: Acknowledging circular dependencies is the first step to fixing them.

#### Quick Check Exercise

Test your understanding with this scenario:

**Scenario**: You're documenting a "user-profile-service" that:
- Stores data in a PostgreSQL database
- Calls an external image-resizing service for profile photos
- Uses a validation library for email format checking
- Provides an API that the mobile app consumes
- Sends notifications through a message queue

**Challenge**: Write the catalog-info.yaml relationships section for this service.

**Your Answer**:
```yaml
spec:
  dependsOn:
    # Add your dependencies here
  
  consumesApis:
    # Add consumed APIs here
    
  providesApis:
    # Add provided APIs here
```

**Solution** (Don't peek until you've tried!):
```yaml
spec:
  dependsOn:
    - resource:default/postgres-user-db
      # Stores user profile data including names, preferences, and settings
    - component:default/email-validation-library
      # Validates email format and checks for common typos
    - resource:default/rabbitmq-notifications
      # Message queue for sending profile update notifications
  
  consumesApis:
    - api:default/image-resizing-api
      # Processes and optimizes user profile photos
    
  providesApis:
    - api:default/user-profile-api
      # Allows mobile app and web app to read/update user profiles
```

This comprehensive approach to explicit relationships transforms your software architecture from a mystery into a clear, navigable system that any team member can understand and work with confidently.
