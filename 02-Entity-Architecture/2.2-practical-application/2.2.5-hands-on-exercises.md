# Hands-On Learning Exercises

## Learning Objectives and Progression Path

This exercise collection is designed to take you from understanding basic relationship concepts to mastering complex architectural decisions. Each difficulty level builds on the previous one, ensuring you develop both theoretical knowledge and practical skills.

### 🎯 Learning Objectives by Level

**Beginner Level (Foundation Building)**
- Identify and document basic relationships in existing systems
- Recognize common relationship documentation problems
- Apply the four core relationship principles in simple scenarios
- Understand the hierarchy rules (System → Feature → Component)

**Intermediate Level (Practical Application)**
- Design relationship solutions for real-world scenarios
- Make architectural decisions about coupling and decoupling
- Plan and execute API versioning strategies
- Organize complex systems into proper hierarchies

**Advanced Level (Mastery and Leadership)**
- Solve complex architectural challenges involving multiple systems
- Design enterprise-scale relationship strategies
- Lead migration and refactoring initiatives
- Create organizational standards and best practices

### 📈 Progression Path

```
Beginner Exercises (1-4 hours)
    ↓
Self-Assessment & Validation
    ↓
Intermediate Exercises (4-8 hours)
    ↓
Self-Assessment & Validation
    ↓
Advanced Exercises (8+ hours)
    ↓
Mastery Validation & Next Steps
```

---

## 🟢 Beginner Level Exercises

*Prerequisites: Understanding of basic entity types and relationship concepts*
*Time Investment: 1-4 hours*
*Learning Focus: Recognition and basic application*

### Exercise B1: The Mystery Service Investigation

**Learning Objective**: Learn to identify undocumented relationships from system behavior

**Scenario**: You've inherited a service called `order-processor` with minimal documentation:

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: order-processor
  description: Processes customer orders
spec:
  type: service
  lifecycle: production
  owner: ecommerce-team
```

**The Problem**: The service keeps failing, but you can't tell what it depends on.

**Error Logs**:
```
ERROR: Connection to database 'inventory_db' failed
ERROR: Timeout calling payment-service API at /api/v1/charge
ERROR: Failed to import validation library 'order-validators'
ERROR: Cannot connect to Redis cache at redis://cache-cluster:6379
```

**Your Task**: 
1. Identify what relationships should be documented
2. Write the explicit relationships in catalog-info.yaml format
3. Explain the business purpose of each relationship

**Solution Template**:
```yaml
spec:
  dependsOn:
    # Add dependencies here with comments explaining why
  
  consumesApis:
    # Add API dependencies here with comments explaining what they do
```

**Self-Check Questions**:
- Did you identify all four types of dependencies from the error logs?
- Did you explain the business purpose of each relationship?
- Would a new developer understand what this service does from your documentation?

---

### Exercise B2: Basic Hierarchy Organization

**Learning Objective**: Apply hierarchy rules to organize simple components

**Scenario**: A junior developer created this hierarchy that violates several rules:

```yaml
# System: E-commerce Platform
spec:
  contains:
    - component:default/user-service      # ❌ Problem 1
    - feature:default/user-management
    - component:default/payment-gateway   # ❌ Problem 2

# Feature: User Management  
spec:
  implementedBy:
    - system:default/authentication-system  # ❌ Problem 3
    - component:default/user-profile-ui
```

**Your Task**:
1. Identify the three hierarchy rule violations
2. Fix the organization following proper hierarchy rules
3. Explain why each rule exists

**Self-Check Questions**:
- Can you state the hierarchy rules from memory?
- Did you fix all three violations?
- Do your fixes create a logical business organization?

---

### Exercise B3: Tight Coupling Recognition

**Learning Objective**: Identify coupling problems and understand their impact

**Scenario**: Two services are causing deployment problems:

```yaml
# user-service
spec:
  dependsOn:
    - component:default/order-service  # Direct database access
    - resource:default/order-database  # Shares order-service's database

# order-service  
spec:
  dependsOn:
    - component:default/user-service   # Direct method calls
    - resource:default/user-database   # Shares user-service's database
```

**Your Task**:
1. Identify the coupling problems
2. Explain why these problems prevent independent deployment
3. Propose a basic decoupling solution using APIs

**Self-Check Questions**:
- Can you explain why shared databases create coupling?
- Do you understand why circular dependencies are problematic?
- Does your solution eliminate the deployment dependency?

---

### Exercise B4: Simple API Versioning

**Learning Objective**: Recognize breaking changes and plan basic migration

**Scenario**: You need to update your user API:

**Current API (v1)**:
```json
POST /api/v1/users
{
  "username": "john_doe",
  "email": "<EMAIL>",    // Optional field
  "password": "secret123"
}
```

**Proposed API (v2)**:
```json
POST /api/v2/users  
{
  "username": "john_doe",
  "email": "<EMAIL>",    // Now required field
  "password": "secret123",
  "email_verified": false         // New field
}
```

**Your Task**:
1. Identify what makes this a breaking change
2. Plan a safe migration strategy
3. Create a timeline for deprecating v1

**Self-Check Questions**:
- Can you explain why making a field required is breaking?
- Does your migration plan protect existing consumers?
- Is your timeline realistic for consumer migration?

---

## 🟡 Intermediate Level Exercises

*Prerequisites: Completed beginner exercises with 80%+ accuracy*
*Time Investment: 4-8 hours*
*Learning Focus: Design and implementation*

### Exercise I1: Microservices Migration Planning

**Learning Objective**: Design comprehensive relationship models for complex systems

**Scenario**: You're breaking apart a monolithic e-commerce application into microservices.

**Current Monolith** handles:
- User authentication and profiles
- Product catalog and search
- Shopping cart management
- Order processing and fulfillment
- Payment processing
- Email notifications
- Admin dashboard

**New Microservices Architecture**:
- `auth-service`: User authentication
- `user-profile-service`: User profile management
- `product-catalog-service`: Product information
- `search-service`: Product search functionality
- `cart-service`: Shopping cart operations
- `order-service`: Order processing
- `payment-service`: Payment processing
- `notification-service`: Email/SMS notifications
- `admin-dashboard`: Management interface

**Shared Resources**:
- `postgres-users-db`, `postgres-products-db`, `postgres-orders-db`
- `redis-cache`, `elasticsearch-cluster`, `rabbitmq-events`

**Your Task**:
1. Design complete relationship models for all 9 services
2. Consider both API-based and event-driven communication patterns
3. Ensure proper data ownership and no shared databases
4. Plan the migration sequence to minimize risk

**Validation Criteria**:
- Each service has clear data ownership
- No circular dependencies between services
- Admin dashboard can access all necessary data
- Migration can be done incrementally

---

### Exercise I2: Library Decoupling Strategy

**Learning Objective**: Design focused libraries that minimize coupling

**Scenario**: Your "utility library" has become a coupling nightmare:

```yaml
# The problematic utility-library contains:
# - Database connection utilities
# - Email sending functions  
# - Payment processing helpers
# - User authentication helpers
# - File upload utilities
# - Logging configuration
# - API rate limiting
```

**Problems**:
- Services needing only logging must include payment processing code
- Email changes require redeploying all services
- Library has become a "god object"

**Your Task**:
1. Break the library into 4-6 focused libraries
2. Design dependency relationships between the new libraries
3. Show how a service needing only logging and file upload would depend on your new design
4. Create a migration plan for existing consumers

**Validation Criteria**:
- Each library has a single, clear responsibility
- Services only depend on libraries they actually use
- Migration plan minimizes disruption to existing services
- New design prevents future coupling problems

---

### Exercise I3: Event-Driven Decoupling Design

**Learning Objective**: Transform tightly coupled systems into event-driven architectures

**Scenario**: You have a tightly coupled order processing flow:

**Current Flow**:
1. `order-service` creates order → directly calls `inventory-service`
2. `inventory-service` reserves items → directly calls `payment-service`  
3. `payment-service` charges card → directly calls `fulfillment-service`
4. `fulfillment-service` ships order → directly calls `notification-service`

**Problems**:
- If any service is down, entire flow fails
- Services are tightly coupled and hard to test
- Adding new steps requires changing multiple services

**Your Task**:
1. Redesign as an event-driven system using message queues
2. Define events each service publishes and subscribes to
3. Show how failures are handled in the new design
4. Document the relationship changes in catalog-info.yaml format

**Validation Criteria**:
- Services are loosely coupled through events
- System can handle individual service failures gracefully
- New steps can be added without changing existing services
- Event flow is clearly documented and traceable

---

### Exercise I4: Multi-Consumer API Evolution

**Learning Objective**: Manage API evolution across multiple consumer types

**Scenario**: Your `order-api` has multiple consumers with different update capabilities:

**Consumers**:
- `mobile-app`: Updates quarterly (slow)
- `web-app`: Updates monthly (medium)
- `admin-dashboard`: Updates weekly (fast)
- `third-party-integration`: Updates rarely (very slow)

**Current Versions**:
- v1: Used by mobile-app and third-party-integration
- v2: Used by web-app
- v3: Used by admin-dashboard

**Challenge**: You need to introduce v4 with significant improvements while supporting all existing consumers.

**Your Task**:
1. Design a versioning strategy that accommodates all consumer types
2. Create migration timelines for each consumer
3. Plan the technical implementation of supporting multiple versions
4. Design a communication plan for API changes

**Validation Criteria**:
- Strategy respects each consumer's update constraints
- Migration timelines are realistic and coordinated
- Technical implementation is maintainable
- Communication plan ensures no surprises for consumers

---

## 🔴 Advanced Level Exercises

*Prerequisites: Completed intermediate exercises with 80%+ accuracy*
*Time Investment: 8+ hours*
*Learning Focus: Leadership and enterprise-scale decisions*

### Exercise A1: Enterprise Architecture Reorganization

**Learning Objective**: Design enterprise-scale hierarchies that support organizational growth

**Scenario**: Your healthcare platform is growing from startup to enterprise scale.

**Current Organization** (startup):
- Single product team (10 people)
- Monolithic application
- Simple hierarchy: System → Components

**Target Organization** (enterprise):
- Patient Experience Division (3 teams, 30 people)
- Provider Experience Division (2 teams, 20 people)
- Operations Division (2 teams, 15 people)
- Platform Division (1 team, 10 people)

**Business Capabilities**:
- Patient registration, appointments, medical records
- Provider scheduling, patient management, billing
- Facility management, staff coordination, compliance
- Authentication, notifications, data analytics

**Your Task**:
1. Design a complete hierarchy that supports the new organizational structure
2. Ensure each division can work independently
3. Identify shared services and governance needs
4. Plan the migration from current to target state

**Validation Criteria**:
- Hierarchy aligns with organizational boundaries
- Teams have clear ownership and autonomy
- Shared services are properly governed
- Migration plan is realistic and low-risk

---

### Exercise A2: Multi-Platform Contract Management

**Learning Objective**: Design contract management strategies for complex ecosystems

**Scenario**: You're managing APIs across a complex ecosystem:

**Internal Services** (fast updates):
- `user-service`, `order-service`, `payment-service`

**Partner Integrations** (medium updates):
- `shipping-partner-api`, `tax-calculation-api`

**Public APIs** (slow updates):
- `developer-api`, `webhook-api`

**Mobile Apps** (very slow updates):
- iOS app, Android app

**Constraints**:
- Internal services deploy daily
- Partner integrations update monthly
- Public APIs must maintain 2+ years backward compatibility
- Mobile apps update quarterly at best

**Your Task**:
1. Design a comprehensive contract management strategy
2. Create versioning policies for each consumer type
3. Plan tooling and automation for contract testing
4. Design governance processes for API changes

**Validation Criteria**:
- Strategy balances innovation with stability
- Policies are appropriate for each consumer type
- Tooling prevents accidental breaking changes
- Governance ensures coordinated evolution

---

### Exercise A3: Legacy System Integration Strategy

**Learning Objective**: Design integration strategies for complex legacy environments

**Scenario**: You're modernizing a 20-year-old financial services platform.

**Legacy Systems** (cannot change):
- `mainframe-core`: COBOL system handling transactions
- `legacy-reporting`: Old Java system with custom database
- `compliance-system`: Vendor system with limited API

**Modern Systems** (can evolve):
- `customer-portal`: New React application
- `mobile-app`: Native iOS/Android apps
- `api-gateway`: New microservices platform

**Integration Challenges**:
- Legacy systems use different data formats
- Some systems only support batch processing
- Compliance requirements limit integration options
- Performance requirements are strict

**Your Task**:
1. Design an integration architecture that bridges legacy and modern systems
2. Plan data synchronization strategies
3. Ensure compliance and audit requirements are met
4. Create a modernization roadmap that minimizes risk

**Validation Criteria**:
- Integration preserves legacy system stability
- Modern systems can evolve independently
- Data consistency is maintained across systems
- Modernization plan provides incremental value

---

## 📊 Self-Assessment and Validation Framework

### Beginner Level Validation

**Knowledge Check** (Complete before moving to Intermediate):
- [ ] I can identify all four relationship types (`dependsOn`, `consumesApis`, `providesApis`, `implementedBy`)
- [ ] I understand the hierarchy rules (System → Feature → Component → Resource)
- [ ] I can recognize basic coupling problems in system designs
- [ ] I know what makes an API change "breaking" vs "non-breaking"

**Practical Skills Check**:
- [ ] I can document relationships for a simple service
- [ ] I can fix basic hierarchy violations
- [ ] I can propose simple decoupling solutions
- [ ] I can plan a basic API migration

**Confidence Indicators**:
- [ ] I feel comfortable explaining relationship concepts to a colleague
- [ ] I can identify relationship problems in my current project
- [ ] I understand why explicit relationships matter for system reliability

### Intermediate Level Validation

**Knowledge Check** (Complete before moving to Advanced):
- [ ] I can design relationship models for complex multi-service systems
- [ ] I understand trade-offs between different decoupling strategies
- [ ] I can plan API evolution strategies for multiple consumer types
- [ ] I can organize complex systems into proper business hierarchies

**Practical Skills Check**:
- [ ] I can lead a microservices decomposition project
- [ ] I can design library architectures that minimize coupling
- [ ] I can implement event-driven decoupling solutions
- [ ] I can manage API versioning across multiple consumers

**Leadership Indicators**:
- [ ] I can mentor others in relationship best practices
- [ ] I can make architectural decisions that balance multiple concerns
- [ ] I can communicate relationship concepts to non-technical stakeholders

### Advanced Level Validation

**Mastery Indicators**:
- [ ] I can design enterprise-scale architectures that support organizational growth
- [ ] I can create governance processes for relationship management
- [ ] I can lead complex migration and modernization initiatives
- [ ] I can establish organizational standards and best practices

**Expert Contributions**:
- [ ] I contribute to tooling that automates relationship discovery
- [ ] I share knowledge through talks, writing, or open source
- [ ] I research and experiment with new architectural patterns
- [ ] I influence industry best practices in my domain

### Continuous Validation Techniques

**Weekly Practice**:
- Review one service's relationships and identify improvements
- Discuss coupling problems with team members
- Practice explaining hierarchy concepts to new team members

**Monthly Assessment**:
- Audit your team's relationship documentation completeness
- Identify and plan fixes for coupling problems in current projects
- Review and update API versioning strategies

**Quarterly Growth**:
- Take on increasingly complex relationship challenges
- Mentor others at lower skill levels
- Contribute to organizational relationship standards

---

## 🎓 Mastery Indicators and Next Steps

### Relationship Mastery Progression

**Beginner Mastery** (Foundation Solid):
- Can document relationships for any component
- Recognizes coupling problems when they see them
- Understands hierarchy rules and can apply them
- Plans basic API migrations successfully

**Intermediate Mastery** (Practical Expert):
- Designs relationship models for complex systems
- Chooses appropriate decoupling strategies for different scenarios
- Manages API evolution across multiple consumer types
- Organizes systems to support team autonomy

**Advanced Mastery** (Architectural Leader):
- Creates enterprise-scale relationship strategies
- Establishes governance processes for relationship management
- Leads complex migration and modernization initiatives
- Influences organizational architecture standards

### Your Next Learning Steps

**If you're at Beginner Level**:
1. **Practice Daily**: Document relationships for one service per week
2. **Find a Mentor**: Work with someone at Intermediate+ level
3. **Start Small**: Fix one coupling problem in your current project
4. **Build Confidence**: Explain concepts to other beginners

**If you're at Intermediate Level**:
1. **Lead Initiatives**: Take ownership of a decoupling or migration project
2. **Mentor Others**: Help beginners learn relationship concepts
3. **Expand Scope**: Work on multi-team or multi-system challenges
4. **Develop Standards**: Create team guidelines for relationship documentation

**If you're at Advanced Level**:
1. **Shape Organization**: Influence enterprise architecture decisions
2. **Create Tools**: Build automation for relationship discovery and validation
3. **Share Knowledge**: Speak at conferences, write articles, contribute to open source
4. **Research Innovation**: Experiment with new architectural patterns and share learnings

### Continuous Learning Resources

**Books and Articles**:
- "Building Microservices" by Sam Newman (coupling and API design)
- "Software Architecture: The Hard Parts" by Ford, Parsons, Kua (architectural decisions)
- "Domain-Driven Design" by Eric Evans (hierarchy and boundaries)

**Practical Projects**:
- Contribute to open source projects with complex architectures
- Document relationships for existing systems in your organization
- Lead migration projects from monolith to microservices
- Create tooling for relationship discovery and validation

**Community Engagement**:
- Join architecture communities and discussion groups
- Attend conferences focused on software architecture
- Participate in architecture review processes
- Share your experiences and learn from others

Remember: Mastery is a journey, not a destination. Each project brings new challenges and opportunities to deepen your understanding of relationship best practices.

---

## 🔧 Quick Reference and Troubleshooting

### Emergency Relationship Debugging

**"My Service Keeps Failing" Checklist**:
1. ✅ Are all `dependsOn` components running and accessible?
2. ✅ Are all `consumesApis` endpoints responding correctly?
3. ✅ Can you connect to all databases and resources?
4. ✅ Have any dependencies changed versions recently?
5. ✅ Are you using compatible API versions?

**"I Don't Know What Will Break" Impact Analysis**:
1. ✅ Who lists you in their `dependsOn`?
2. ✅ Who calls your `providesApis`?
3. ✅ What services consume your events or data?
4. ✅ Have you tested all relationship scenarios?
5. ✅ Do you have a rollback plan ready?

### Common Patterns Quick Reference

**Microservices Pattern**:
```yaml
System: Business Domain
├── Feature: User Capability
│   ├── Service: Implementation
│   ├── API: Interface Contract
│   └── Resource: Data Store
```

**Event-Driven Pattern**:
```yaml
Service A:
  publishes: [event-type-1]
  
Service B:
  subscribes: [event-type-1]
  publishes: [event-type-2]
```

**API Versioning Pattern**:
```yaml
API v1: Stable, deprecated
API v2: Current, supported  
API v3: Preview, experimental
```

This comprehensive exercise framework provides structured learning from basic concepts to enterprise mastery, with clear validation criteria and continuous improvement guidance.

## Next Steps: From Practice to Mastery

Congratulations! You've completed the practical application section. You now have hands-on experience with all the core relationship practices. Here's how to continue your journey:

**→ Advance to Comprehensive Framework:**
- **[2.3.1 Essential Dimensions Overview](../2.3-comprehensive-framework/2.3.1-essential-dimensions-overview.md)** - Learn the complete dimensional framework for advanced architecture modeling
- **[2.3.2 Dimensional Documentation](../2.3-comprehensive-framework/2.3.2-dimensional-documentation.md)** - Master comprehensive entity documentation
- **[2.3.3 Implementation Patterns](../2.3-comprehensive-framework/2.3.3-implementation-patterns.md)** - Apply advanced architectural patterns

**Quick Reference for Daily Work:**
- **[2.4.1 Quick Reference Cards](../2.4-reference/2.4.1-quick-reference-cards.md)** - Keep these handy for quick decisions and validation
- **[2.4.2 Troubleshooting Guide](../2.4-reference/2.4.2-troubleshooting-guide.md)** - Solutions for problems you'll encounter in practice
- **[2.4.3 Complete Examples](../2.4-reference/2.4.3-complete-examples.md)** - Reference implementations for complex scenarios

**Review and Reinforce:**
- **[2.1 Foundations](../2.1-foundations/2.1.1-entity-model-introduction.md)** - Revisit core concepts as needed
  - **[2.1.1 Entity Model Introduction](../2.1-foundations/2.1.1-entity-model-introduction.md)** - Review the foundational concepts
  - **[2.1.2 Entity Types and Hierarchy](../2.1-foundations/2.1.2-entity-types-and-hierarchy.md)** - Reinforce hierarchy rules
  - **[2.1.3 Relationship Fundamentals](../2.1-foundations/2.1.3-relationship-fundamentals.md)** - Review the four core relationship types
- **[2.2.1 Documenting Relationships](./2.2.1-documenting-relationships.md)** - Review systematic documentation approaches
- **[2.2.2 Designing Loose Coupling](./2.2.2-designing-loose-coupling.md)** - Reinforce decoupling strategies
- **[2.2.3 Organizing Hierarchies](./2.2.3-organizing-hierarchies.md)** - Practice hierarchy organization
- **[2.2.4 Managing API Versions](./2.2.4-managing-api-versions.md)** - Apply versioning strategies

**Apply to Real Projects:**
- Start with small, low-risk components in your current system
- Apply one technique at a time and measure the results
- Build confidence through incremental success
- Share your learnings with your team
- Use the validation checklists to ensure quality

**Continue Learning:**
- Return to exercises at higher difficulty levels as you gain experience
- Create your own exercises based on challenges in your organization
- Mentor others using these materials and exercises
- Contribute improvements and new scenarios to help others learn

---

*Remember: The goal isn't perfect architecture from day one—it's continuous improvement through systematic application of relationship best practices. Every small improvement makes your system more understandable, maintainable, and reliable.*